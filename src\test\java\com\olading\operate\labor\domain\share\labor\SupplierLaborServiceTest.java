package com.olading.operate.labor.domain.share.labor;

import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.CustomerQuery;
import com.olading.operate.labor.domain.query.LaborQuery;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SupplierLaborService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SupplierLaborServiceTest extends BaseTest {

    @Autowired
    SupplierLaborService laborService;

    @Autowired
    QueryService queryService;

    final Long supplierId = 1L;
    final Long contractId = 2L;
    final Long customerId = 3L;
    final TenantInfo tenantId = new TenantInfo(TenantInfo.TenantType.SUPPLIER, "1");
    final String idCard = "123456789012345658";
    final String cellphone = "13800138001";
    final String name = "张三";

    @Test
    public void createSupplierLabor() {
        SupplierLaborVo param = new SupplierLaborVo();
        param.setSupplierId(supplierId);
        param.setCustomerId(customerId);
        param.setContractId(contractId);
        param.setCorporationId(1L);
        param.setIdCard(idCard);
        param.setCellphone(cellphone);
        param.setName("张三");
//        param.setChildren("无");
//        param.setAddress("杭州XXXXXXXXXX");
//        param.setBankCard("598652365666699555");
//        param.setBankBranch("CC支行");
//        param.setCardBank("农业银行");
        Long laborId = laborService.createSupplierLabor(param, supplierId, tenantId);
        log.info("创建劳务人员成功，ID:{}", laborId);
    }

    @Test
    public void getSupplierLaborDetail() {
        Long laborId = 10L;
        SupplierLaborVo labor = laborService.getSupplierLaborDetail(laborId, supplierId);
        log.info("查询劳务人员详情成功，{}", labor);
    }

    @Test
    public void updateSupplierLabor() {
        Long laborId = 10L;
        SupplierLaborVo labor = laborService.getSupplierLaborDetail(laborId, supplierId);
        labor.setPersonalEmail("<EMAIL>");
        labor.setMobileNumber("***********");
        labor.setId(laborId);
        laborService.updateSupplierLabor(labor);
        log.info("更新劳务人员成功");
    }

    @Test
    public void queryLaborTest() {

        LaborQuery.Filters filters = new LaborQuery.Filters();
//        filters.setName("70");


        // 2. 构建分页过滤器
        QueryFilter<LaborQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(filters);
        queryFilter.setLimit(10L);
        queryFilter.setOffset(0L);


        DataSet<LaborQuery.Record> recordDataSet = queryService.queryLabor(queryFilter);
        log.info("查询结果：{}", recordDataSet);
    }

}
