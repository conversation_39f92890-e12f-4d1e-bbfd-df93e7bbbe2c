option java_package = "com.lanmaoly.cloud.psalary.mybank";

option java_serializable = true;


message Header{
    string code;
}

/**
 * 卡属性
 */
enum CardAttribute {
    /**
     * 对私
     */
    C = 0;
    /**
     * 对公
     */
    B = 1;
    /**
     * 支付宝
     */
    ALIPAY = 2;
    /**
     * 微信
     */
    WXPAY = 3;
}

/**
 * 卡属性
 */
enum DiAccountTypeEnum {
    /**
     * 普通直连
     */
    MERCHANT = 0;
    /**
     * 集团公司
     */
    GROUP = 1;
    /**
     * 集团子公司
     */
    CHILD = 2;
    /**
     * SaaS直连
     */
    SAAS = 3;
}

/**
 * 网商银行接口调用状态
 */
enum ResultCode {
    /**
     * 失败
     */
    F = 0;
    /**
     * 成功
     */
    T = 1;
}

/**
 * 网商银行交易状态
 */
enum MybankRemitStatus {
    /**
     * 初始化
     */
    INIT = 0;
    /**
     * 出款中
     */
    REMITTING = 1;
    /**
     * 提现失败
     */
    FAIL = 2;
    /**
     * 提现成功
     */
    SUCCESS = 3;
    /**
     * 退票成功
     */
    REFUND = 4;
    /**
     * 待确认订单
     */
    UNCONFIRMED = 5;

    /**
     * 订单不存在
     */
    NONE = 6;
}

enum TransType {
    /**
     * 入款
     */
    C = 0;
    /**
     * 出款
     */
    D = 1;
}

/**
 * 出款请求参数
 */
message MybankRemitParam {
    /**
     * 出款方ID
     */
    string sourceId;
    /**
     * 流水号
     */
    string requestNo;
    /**
     * 银行卡号/支付宝号
     */
    string bankcardNo;
    /**
     * 银行卡户明/支付宝名
     */
    string accountName;
    /**
     * 银行名称
     */
    string bankName;
    /**
     * 支行号(卡属性对公时非空)
     */
    string bankBranchNo;
    /**
     * 支行名称(卡属性对公时非空)
     */
    string bankBranchName;
    /**
     * 卡属性(对公/对私/支付宝)
     */
    CardAttribute cardAttribute;
    /**
     * 金额
     */
    decimal amount;
    /**
     * 服务费金额
     */
    decimal feeAmount;
    /**
     * 备注
     */
    string memo;
    /**
     * 出款方域
     */
    string sourceDomainCode;
    /**
     * 入款方域
     */
    string targetDomainCode;
    /**
     * 微信 商户账号appid
     */
    string appId;
    /**
     * 商户ID
     */
    int64 merchantId;
    /**
     * 专普票模式
     */
    string taxModel;
    /**
     * 用途
        CHANNEL_WITHDRAW 通道 工资发放,
        CHANNEL_WHITE_REFUND 通道 白名单退款(无需对账),
        CHANNEL_MERCHANT_WITHDRAW 企业通道提现,
        CHANNEL_AGENT_WITHDRAW 资质方/地通道提现/阿拉丁提现,
        CHANNEL_RECHARGE 通道充值,
     */
    string usedFor;

    /**
    * 收款人卡号
    */
    string idCard;
    /**
    * 收款人手机号
    */
    string mobile;
    /**
    * 用工企业名称
    */
    string merchantName;
    /**
    * 用工企业统一社会信用代码
    */
    string unifiedCode;
    /**
     * 批次号
    */
    string batchNo;

}

/**
 * 出款结果参数
 */
message MybankRemitResult {
    /**
     * T:成功 F:失败
     */
    ResultCode resultCode;
    /**
     * 错误码
     */
    string errorCode;
    /**
     * 错误原因
     */
    string errorMessage;
    /**
     * 备注
     */
    string memo;
}

/*
 * 网商银行 账户结果参数
 */
message MybankAccountResult {
    /**
     * T:成功 F:失败
     */
    ResultCode resultCode;
    /**
 * 银行进件开户状态
 * REGISTER_AUDIT：审核中
 * REGISTER_SUCCESS：进件成功
 * REGISTER_FAIL：进件失败
 * OPEN_SUCCESS：子户开立成功
 * OPEN_AUDIT：子户开立中
 * OPEN_FAIL:子户开立失败
    */
    string accountBankStatus;
        /**
        *银行开户返回错误信息
        */
    string accountBankFailReason;
    /**
     * 失败原因
     */
    string errorMessage;
    /**
     * 网商银行UID
     */
    string uid;
    /**
     * 账号
     */
    string accountNo;
    /**
     * 开户名称
     */
    string accountName;
    /**
     * 开户银行
     */
    string bankName;
    /*
     * 支行名称
     */
    string branchName;
    /**
     * 开立子户账号（网商3.0）
     */
    string subAccountNo;
    /**
     * 协议签约链接（易宝）
     */
    string contractUrl;
     /**
      * 是否注销(1表示已注销)
      */
    int32 logout;
}

/*
 * 网商银行 交易查询请求参数
 */
message MybankQueryParam {
    /**
     * 出款方ID
     */
    string sourceId;
    /**
     * 流水号
     */
    string requestNo;
    /**
     * 查询时间
     */
    datetime queryDate;
    /**
     * 出款方域
     */
    string sourceDomainCode;
    /**
     * 入款方域
     */
    string targetDomainCode;
}

/*
 * 网商银行 交易查询结果参数
 */
message MybankQueryResult {
    /**
     * 流水号
     */
    string requestNo;
    /**
     * 状态
     */
    MybankRemitStatus status;
    /**
     * 错误码
     */
    string errorCode;
    /**
     * 错误信息
     */
    string errorMessage;
    /**
     * 备注
     */
    string memo;
}

/*
 * 招商 交易查询结果参数
 */
message CmbQueryResult {
    /**
     * 结果List
     */
    list<MybankQueryResult> resultList;
}

/*
 * 网商银行 充值异步通知结果参数
 */
message MybankRechargeResult {
    /**
     * 流水号
     */
    string requestNo;
    /**
     * 通道
     */
    string targetDomainEnum;
    /**
     * 资质地ID
     */
    string sourceId;
    /**
     * 金额
     */
    decimal amount;
    /**
     * 付款方卡号
     */
    string payerCardNo;
    /**
     * 付款方卡名称
     */
    string payerCardName;
    /**
     * 付款方联行号
     */
    string payerBankOrgId;

    /**
     * 付款方备注
     */
    string payerRemark;

     /**
     * 收款方卡号
     */
    string payeeCardNo;

    /**
     * 收款方户名
     */
    string payeeCardName;
    /**
     * 专普票模式
     */
    string taxModel;
    /**
     * 充值凭证
     */
    string voucherFile;

}

/*
 * 招商 交易结果参数
 */
message CmbRechargeResult {
    /**
     * 结果List
     */
    list<MybankRechargeResult> resultList;
}

/**
 * 网商银行相关接口
 */
service RemitService{

    /**
     * 出款
     */
    remit(MybankRemitParam) returns (MybankRemitResult);

    /**
     * 交易详情查询 同步银行状态
     */
    queryRemit(MybankQueryParam) returns (MybankQueryResult);

    /**
     * 交易详情查询 同步银行状态
     * 根据交易日期查询记录
     */
    queryRemitByDate(MybankQueryParam) returns (CmbQueryResult);

    /**
     * 交易记录查询 不同步银行状态
     */
    queryRemitRecord(MybankQueryParam) returns (MybankQueryResult);

    /**
     * 招商银行交易记录 自动上账
     */
    queryRechargeRecord(MybankQueryParam) returns(CmbRechargeResult);

    /**
     * 创建网商银行UID关联关系
     */
    createMybankAccount(MybankAccountParam{
        /**
         * 出款方ID
         */
        string sourceId;
        /**
         * 出款方域
         */
        string sourceDomainCode;
        /**
         * 入款方域
         */
        string targetDomainCode;
        /**
         * 网商银行UID
         */
        string uid;
        /**
         * 网商银行账户号
         */
        string accountNo;
        /**
         * 网商银行平台ID
         */
        string partnerId;
        /**
         * 银行公钥
         */
        string rsaPublicKey;
        /**
         * 网商渠道编码
         */
        string whiteChannelCode;
        /**
         * 网商开户名称(企业名称)
         */
        string accountName;
        /**
         * 支行号
         */
        string bankBranchNo;
        /**
         * 调用地址
         */
        string remoteUrl;
        /*
        * 微信appid
        * 中信银行虚户ID
        */
        string appId;
        /*
         * 微信证书地址
         */
        string certUrl;
        /*
         * 开户行
         */
        string bankName;

        /**
         * 加密类型:AES/SM4
         */
        string encryType;
        /*
         * aes私钥
         */
        string aesPrivateKey;
        /*
         * rsa 私钥
         */
        string rsaPrivateKey;
        /*
         * sm4私钥
         */
        string sm4PrivateKey;
        /*
         * 国密 私钥
         */
        string smPrivateKey;
         /*
         * 支行名称
         */
        string branchName;
        /**
        *招行审批是否开启
        */
        bool enableApply;

        /**
        *商户ID
        */
        int64 merchantId;

        /**
        *填写营业执照号或统一社会信用代码
        */
        string orgNo;
        /**
        *法人证件号码
        */
        string principalCertNo;
        /**
        *法人姓名
        */
        string principalPerson;
        /**
        *法人身份证正面
        */
        string certPhotoA;
        /**
        *法人身份证反面
        */
        string certPhotoB;
        /**
        *营业执照图片
        */
        string licensePhoto;
        /**
        *营业执照编号
        */
        string licenseNo;
        /**
        *开户许可证照片
        */
        string industryLicensePhoto;
        /**
        *开户许可证编号
        */
        string industryLicenseNo;
        /**
        *代发商户资质材料图片
        */
        string qualificationPhoto;
        /**
        *代发商户和上游甲方合作真实协议
        */
        string upstreamCoPhoto;
        /**
        *代发商户和下游C端或个体户合作真实协议
        */
        string downstreamCoPhoto;
        /**
        *代发商户一次性授权协议（图片类型）
        */
        string authorizeCoPhoto;
        /**
        *联系邮箱
        */
        string email;
        /**
        *经营地址
        */
        string address;

        string cebBusinessNo;
        string cebBusinessName;
        string cebCashMoneyNo;
        string cebSelfMoneyNo;
        string cebNameNo;
        string cebIdCardNo;
        string cebBankCardNo;
        string cebOpenBankName;
        string cebPhone;
        string cebAccountType;
        /**
        *是否刪除账户
        */
        boolean delete;

        /**
         * 入金账户号
         */
        string childBankNo;
        /**
         * 入金账户开户银行全称
         */
        string childBankName;
        /**
         * 入金账户联行号
         */
        string childBankBranchNo;

        /**
         * 账户类型
         */
        DiAccountTypeEnum diAccountType;

        /**
         * 子账户
         */
        string subAccountNo;

        /**
         * 来源
        */
        string source;

        string signedName;
        string province;
        string city;
        string businessAddress;
        string contactName;
        string contactEmail;
        string contactPhone;
        string businessClassification;
        string upayBankName;
        string bankCardNo;
        string openProvince;
        string openCity;
        string legalPersonPhone;
        string legalPersonIdType;
        string idEffectiveDateStart;
        string idEffectiveDateEnd;
        string sellingArea;
        string staffSize;
        string desireAuthOtherPath;
        string holdingIdCardPath;
        string otherCerPath;
        string storePath;
        string beneficiaryName;
        string beneficiaryIdType;
        string beneficiaryCertNo;
        string effectiveDateStart;
        string effectiveDateEnd;
        string beneficiaryAddress;
        string equityRatio;
        string belongType;
        string token;
        string taskId;
        string rateId;

    }) returns (MybankAccountResult);

    /**
     * 查询网商银行账户号
     */
    queryMybankAccount(MybankQueryAccountParam {
        /**
         * 出款方ID
         */
        string sourceId;
        /**
         * 出款方域
         */
        string sourceDomainCode;
        /**
         * 入款方域
         */
        string targetDomainCode;
        /**
         * 企业Id
         */
        string merchantId;
        /**
         * CEB_ZZD, 光大银行-资质地(用于收取服务费的会员户，本质与商户开通的会员户一致)
         * CEB_MERCHANT,光大银行-商户
         * CEB_OLD,光大银行-阿拉钉系统服务费账户
         */
        string cebAccountType;
    }) returns (MybankAccountResult);


    /**
     * 退汇
     */
    refund(RefundParam{
        int64 id;
        /**
         * 退汇备注
         */
        string remark;
        /**
         * 出款通道
         */
        string targetDomainCode;
        /**
         * 网商银行用， 是否调用银行退票接口
         */
        string wsIfRefund;
    }) returns (Header);

    /**
     * 人工置为失败
     */
    fail(FailParam{
        int64 id;
        /**
         * 备注
         */
        string remark;
        /**
         * 中信-是否转回
         */
        string ifTransBack;
        /**
         * 出款通道
         */
        string targetDomainCode;
    }) returns (Header);

    /**
     * 网商账户转账
     */
    transfer(MybankTransferParam{
        /**
         * 资质地ID
         */
        string sourceId;
        /**
         * 来源域
         */
        string sourceDomainCode;
        /**
         * 目标域
         */
        string targetDomainCode;
        /**
         * 金额
         */
        decimal amount;
        /**
         * 付款方卡号
         */
        string payerCardNo;
        /**
         * 付款方卡名称
         */
        string payerCardName;
        /**
         * 备注
         */
        string remark;
        /**
        * add by wyl ZC-3301 网商银行调账新增“调账类型”
        * 调账类型 enterprise-企业 personal-个人
        */
        string transferType;
    }) returns (MybankTransferResult{
        /**
         * T:成功 F:失败
         */
        ResultCode resultCode;
        /**
         * 错误码
         */
        string errorCode;
        /**
         * 错误原因
         */
        string errorMessage;
    });

    /**
     * 生成凭证
     */
    createVoucher(CreateVoucherParam{
        /**
         * 请求流水号
         */
        string requestNo;
         /**
         * 出款方域
         */
        string sourceDomainCode;
        /**
         * 入款方域
         */
        string targetDomainCode;
    }) returns (CreateVoucherResult{
        /**
         * T:成功 F:失败
         */
        ResultCode resultCode;
        /**
         * 错误原因
         */
        string errorMessage;
    });
}