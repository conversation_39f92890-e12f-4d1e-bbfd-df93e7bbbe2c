package com.olading.operate.labor.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.lanmaoly.util.lang.StringUtils;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.salary.SalaryCalculateController;
import com.olading.operate.labor.app.web.biz.salary.vo.ImportPayrollStaffRow;
import com.olading.operate.labor.app.web.biz.salary.vo.ImportPreviousIncomeRow;
import com.olading.operate.labor.app.web.biz.salary.vo.PreviousIncomeImportRequest;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.salary.*;
import com.olading.operate.labor.domain.salary.engine.SalaryTaxCalculationService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.util.validation.validator.IDNumberValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class SalaryCalculateService {

    private static final Pattern PHONE_NUMBER_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    private final LaborInfoRepository laborInfoRepository;
    private final CorporationManager corporationManager;
    private final SalaryManager salaryManager;
    private final SalaryTaxCalculationService salaryTaxCalculationService;

    private static final IDNumberValidator idNumberValidator = new IDNumberValidator();


    public void verifyImportPayrollStaffRow(SalaryCalculateController.payrollAddRequest request, List<ImportPayrollStaffRow> results, Map<OwnerType, Set<Long>> ownerTypeSetMap) {


        for (ImportPayrollStaffRow result : results) {
            if (StringUtils.isBlank(result.getName())) {
                result.pushError(result.getName(), "姓名不能为空", "");
            }
            if (StringUtils.isBlank(result.getIdCard())) {
                result.pushError(result.getIdCard(), "身份证号不能为空", "");
            } else if (!idNumberValidator.validate(result.getIdCard())) {
                result.pushError(result.getIdCard(), "身份证号格式不正确", "");
            }else {
                validateAge(result,request.getSupplierCorporationId());
            }

            SupplierLaborEntity laborEntity = laborInfoRepository.findUniqueLaborByIdCard(request.getSupplierCorporationId(), result.getIdCard(), result.getName());
            if (laborEntity == null || laborEntity.isDeleted()) {
                result.pushError(result.getName(), "未找到该员工", "");
                continue;
            }

            LaborInfoEntity laborInfoEntity = laborInfoRepository.findLaborInfoByCorporationAndContract(request.getSupplierCorporationId(), laborEntity.getId(), request.getContractId());
            if (laborInfoEntity == null) {
                result.pushError(result.getName(), "人员不在服务合同下", "");
            }
            result.setCellPhone(laborEntity.getCellphone());
        }

    }

    private void validateAge(ImportPayrollStaffRow result, Long supplierCorporationId) {
        // 从身份证提取出生日期
        String idCard = result.getIdCard();
        String birthDateStr = null;

        // 处理15位和18位身份证
        if (idCard.length() == 15) {
            birthDateStr = "19" + idCard.substring(6, 12);  // 15位身份证：6-12位是YYMMDD
        } else if (idCard.length() == 18) {
            birthDateStr = idCard.substring(6, 14);         // 18位身份证：6-14位是YYYYMMDD
        }

        if (birthDateStr == null) {
            // 非标准长度身份证处理
            result.pushError(result.getIdCard(), "不支持的身份证号长度", "");
            return;
        }

        try {
            // 解析出生日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate currentDate = LocalDate.now();

            // 计算周岁年龄
            Period period = Period.between(birthDate, currentDate);
            int age = period.getYears();

            // 获取作业主体配置
            CorporationConfigEntity config = corporationManager.queryCorporationConfig(supplierCorporationId);

            // 处理年龄限制（支持单边界和双边界校验）
            validateAgeAgainstConfig(result, age, config);

        } catch (DateTimeParseException e) {
            // 日期解析失败处理
            result.pushError(result.getIdCard(), "身份证号包含无效的出生日期", "");
        }
    }

    private void validateAgeAgainstConfig(ImportPayrollStaffRow result, int age, CorporationConfigEntity config) {
        Integer minAge = config.getMinAgeLimit();
        Integer maxAge = config.getMaxAgeLimit();

        // 如果没有任何年龄限制，直接返回
        if (minAge == null && maxAge == null) {
            return;
        }

        // 检查是否超出年龄范围
        boolean outOfRange = (minAge != null && age < minAge) ||
                (maxAge != null && age > maxAge);

        if (!outOfRange) {
            return; // 年龄在有效范围内
        }

        // 构建友好的范围描述
        String rangeDesc;
        if (minAge != null && maxAge != null) {
            rangeDesc = minAge + "周岁-" + maxAge + "周岁";
        } else if (minAge != null) {
            rangeDesc = "≥" + minAge + "周岁";
        } else {
            rangeDesc = "≤" + maxAge + "周岁";
        }

        // 添加错误信息
        result.pushError(
                result.getIdCard(),
                "年龄需在作业主体年龄范围: " + rangeDesc + " 之间（当前年龄：" + age + "周岁）",
                ""
        );
    }

    private boolean isValidPhoneNumber(String phoneNumber) {
        return PHONE_NUMBER_PATTERN.matcher(phoneNumber).matches();
    }

    @Transactional(rollbackFor = Exception.class)
    public void addPayrollStaff(SalaryCalculateController.payrollAddRequest request, List<ImportPayrollStaffRow> successDataList, TenantInfo tenant) {

        YearMonth yearMonth = YearMonth.parse(request.getTaxPeriod());

        SalaryStatementEntity salaryStatementEntity = new SalaryStatementEntity();
        //salaryStatementEntity.setTenantId(tenant);
        salaryStatementEntity.setSupplierId(request.getSupplierId());
        salaryStatementEntity.setCustomerId(request.getCustomerId());
        salaryStatementEntity.setContractId(request.getContractId());
        salaryStatementEntity.setSupplierCorporationId(request.getSupplierCorporationId());
        salaryStatementEntity.setTaxPeriod(request.getTaxPeriod());
        salaryStatementEntity.setTaxDeclarationMonth(yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        salaryStatementEntity.setTotalPeople((long) successDataList.size());
        salaryStatementEntity.setStatus(SalaryStatementStatus.CALCULATING);
        salaryStatementEntity.setUploadTime(LocalDateTime.now());
        salaryManager.saveSalaryStatement(salaryStatementEntity);

        Long salaryStatementEntityId = salaryStatementEntity.getId();
        successDataList.forEach(model -> {
            SalaryDetailEntity salaryDetailEntity = new SalaryDetailEntity();
            salaryDetailEntity.setSalaryStatementId(salaryStatementEntityId);
            salaryDetailEntity.setName(model.getName());
            salaryDetailEntity.setIdCard(model.getIdCard());
            salaryDetailEntity.setPhoneNumber(model.getCellPhone());
            salaryDetailEntity.setPayableAmount(new BigDecimal(model.getAmount()));

            salaryDetailEntity.setTaxPeriod(request.getTaxPeriod());
            salaryDetailEntity.setPayableAmount(new BigDecimal(StringUtils.isBlank(model.getAmount()) ? "0.00" : model.getAmount()));
            salaryDetailEntity.setTaxFreeIncome(new BigDecimal(StringUtils.isBlank(model.getCurrentDeductionIncome()) ? "0.00" : model.getCurrentDeductionIncome()));
            salaryDetailEntity.setOtherDeductions(new BigDecimal(StringUtils.isBlank(model.getOtherDeduction()) ? "0.00" : model.getOtherDeduction()));
            salaryDetailEntity.setTaxReliefAmount(new BigDecimal(StringUtils.isBlank(model.getReductionTax()) ? "0.00" : model.getReductionTax()));
            salaryDetailEntity.setSupplierCorporationId(request.getSupplierCorporationId());
            salaryDetailEntity.setAccumulatedIncome(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedExpenses(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedDeductionExpenses(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedOtherDeductions(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxRelief(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxableAmount(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxAmount(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedPrepaidTax(BigDecimal.ZERO);
            salaryDetailEntity.setCurrentTaxAmount(BigDecimal.ZERO);
            salaryDetailEntity.setCurrentWithholdingTax(BigDecimal.ZERO);
            salaryDetailEntity.setNetPayment(BigDecimal.ZERO);
            salaryDetailEntity.setVatAmount(BigDecimal.ZERO);
            salaryDetailEntity.setAdditionalTaxAmount(BigDecimal.ZERO);
            salaryDetailEntity.setUrbanConstructionTax(BigDecimal.ZERO);
            salaryDetailEntity.setEducationSurcharge(BigDecimal.ZERO);
            salaryDetailEntity.setLocalEducationSurcharge(BigDecimal.ZERO);



            salaryManager.saveSalaryDetail(salaryDetailEntity);
        });
    }

    public void verifyPreviousIncomeInfo(PreviousIncomeImportRequest request, List<ImportPreviousIncomeRow> results, Map<OwnerType, Set<Long>> ownerTypeSetMap) {
        results.forEach(result -> {
            if (StringUtils.isBlank(result.getName())) {
                result.pushError(result.getName(), "姓名不能为空", "");
            }
            if (StringUtils.isBlank(result.getIdCard())) {
                result.pushError(result.getIdCard(), "身份证号不能为空", "");
            } else if (!idNumberValidator.validate(result.getIdCard())) {
                result.pushError(result.getIdCard(), "身份证号格式不正确", "");
            }

            SupplierLaborEntity laborEntity = laborInfoRepository.findUniqueLaborByIdCard(request.getSupplierCorporationId(), result.getIdCard(), result.getName());
            if (laborEntity == null || laborEntity.isDeleted()) {
                result.pushError(result.getName(), "未找到该员工", "");
            }


        });
    }

    public void verifyPayrollStaff(SalaryCalculateController.payrollAddRequest request) {
        List<SalaryStatementEntity> salaryStatementEntities = salaryManager.querySalaryStatement(Collections.singletonList(request.getTaxPeriod()), request.getSupplierCorporationId());

        if (CollectionUtil.isNotEmpty(salaryStatementEntities)){
            salaryStatementEntities.forEach(salaryStatementEntity -> {
                if (salaryStatementEntity.getStatus() != SalaryStatementStatus.CONFIRMED) {
                    throw new ApiException("存在未确认的工资表，请确认后重试",ApiException.API_PARAM_ERROR);
                }
            });
        }

    }

    public void verifyCurrentMonthPayrollStaff(PreviousIncomeImportRequest request) {

        List<PreviousIncomeDeductionEntity> entities = salaryManager.queryPreviousIncomeDeduction(request.getTaxPeriod(), request.getSupplierCorporationId());
        if (CollectionUtil.isNotEmpty(entities)){
            throw new ApiException("当前作业主体本月已存在上期收入减除，请勿重复导入",ApiException.API_PARAM_ERROR);
        }

        //计算下个月的 taxPeriod
        YearMonth current = YearMonth.parse(request.getTaxPeriod());
        YearMonth nextMonth = current.plusMonths(1);
        //如果下个税款所属期是1月，直接返回
        if (nextMonth.getMonthValue() == 1) {
            return;
        }
        String nextTaxPeriod = nextMonth.toString(); // 格式化为 "yyyy-MM"
        List<SalaryStatementEntity> salaryStatementEntities = salaryManager.querySalaryStatement(Collections.singletonList(nextTaxPeriod), request.getSupplierCorporationId());
        if (CollectionUtil.isNotEmpty(salaryStatementEntities)){
            throw new ApiException("当前作业主体本月已存在工资表，无法导入上期收入减除",ApiException.API_PARAM_ERROR);
        }
    }

    public void addPreviousIncomeStaff(PreviousIncomeImportRequest request, List<ImportPreviousIncomeRow> successDataList, TenantInfo tenant) {
        successDataList.forEach(model -> {
            PreviousIncomeDeductionEntity entity = new PreviousIncomeDeductionEntity();
            //entity.setTenantId(tenant.getId());
            entity.setSupplierCorporationId(request.getSupplierCorporationId());
            entity.setFullName(model.getName());
            entity.setIdNumber(model.getIdCard());
            entity.setTaxPeriod(request.getTaxPeriod());
            entity.setAccumulatedIncome(new BigDecimal(StringUtils.isBlank(model.getTotalIncome()) ? "0.00" : model.getTotalIncome()));
            entity.setAccumulatedExpenses(new BigDecimal(StringUtils.isBlank(model.getTotalExpenses()) ? "0.00" : model.getTotalExpenses()));
            entity.setAccumulatedTaxFreeIncome(new BigDecimal(StringUtils.isBlank(model.getTotalTaxFreeIncome()) ? "0.00" : model.getTotalTaxFreeIncome()));
            entity.setAccumulatedOtherDeductions(new BigDecimal(StringUtils.isBlank(model.getTotalOtherDeductions()) ? "0.00" : model.getTotalOtherDeductions()));
            entity.setAccumulatedPrepaidTax(new BigDecimal(StringUtils.isBlank(model.getTotalPrepaidTaxes()) ? "0.00" : model.getTotalPrepaidTaxes()));
            entity.setAccumulatedTaxReductions(new BigDecimal(StringUtils.isBlank(model.getTotalTaxReductions()) ? "0.00" : model.getTotalTaxReductions()));
            entity.setUploadTime(LocalDateTime.now());

            salaryManager.savePreviousIncomeDeduction(entity);
        });
    }

    public void salaryTaxCalculateTask() {
        log.info("[工资表算税]开始计算工资批次的个税");
        List<SalaryStatementEntity> list = salaryManager.querySalaryStatementByStatus(SalaryStatementStatus.CALCULATING);
        if (CollectionUtil.isEmpty(list)) {
            log.info("[工资表算税]没有需要计算的工资批次!");
        }
        for (SalaryStatementEntity salaryStatement : list) {
            log.info("[工资表算税]开始计算工资批次ID: {}, 税期: {}", salaryStatement.getId(), salaryStatement.getTaxPeriod());
            salaryTaxCalculationService.batchCalculateSalaryTax(salaryStatement.getId());
        }
        log.info("[工资表算税]所有工资批次的个税计算完成");
    }
}