package com.olading.operate.labor.domain.share.contract;

import com.lanmaoly.util.lang.StringUtils;
import com.olading.operate.labor.app.web.biz.enums.BusinessContractStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.ManageCalculationRuleEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.CustomerManager;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

import java.util.function.Function;

@Transactional
@Component
@RequiredArgsConstructor
public class BusinessContractManager {

    private final EntityManager em;

    private final CustomerManager customerManager;
    private final CorporationManager corporationManager;

    public BusinessContractEntity addContract(TenantInfo tenantInfo, ContractVo vo) {
        BusinessContractEntity entity = new BusinessContractEntity(tenantInfo);
        copyToEntity(vo, entity);
        entity.setSupplierId(vo.getSupplierId());
        entity.setStatus(BusinessContractStatusEnum.INIT.name());
        BusinessContractEntity contract = em.merge(entity);

        BusinessContractConfigEntity config = new BusinessContractConfigEntity();
        copyToConfig(vo, config);
        config.setContractId(contract.getId());
        em.merge(config);
        return contract;
    }

    public void updateContract(ContractVo vo) {
        BusinessContractEntity entity = getContractById(vo.getId());
        if(entity == null){
            throw new IllegalStateException("服务合同不存在");
        }
        copyToEntityWithNullCheck(vo, entity);
        em.merge(entity);

        BusinessContractConfigEntity config = getConfigByContractId(vo.getId());
        if (config != null) {
            copyToConfigWithNullCheck(vo, config);
            em.merge(config);
        }
    }

    private void copyToEntityWithNullCheck(ContractVo vo, BusinessContractEntity entity) {
        if (vo.getCustomerId() != null) {
            entity.setCustomerId(vo.getCustomerId());
        }
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getName() != null) {
            entity.setName(vo.getName());
        }
        if (vo.getSn() != null) {
            entity.setSn(vo.getSn());
        }
        if (vo.getTimeFixed() != null) {
            entity.setTimeFixed(vo.getTimeFixed());
        }
        if (vo.getStartDate() != null) {
            entity.setStartDate(vo.getStartDate());
        }
        if (vo.getEndDate() != null) {
            entity.setEndDate(vo.getEndDate());
        }
        if(vo.getStartDate() == null && vo.getEndDate() == null){
            entity.setStartDate(null);
            entity.setEndDate(null);
        }
        if (vo.getStopped() != null) {
            entity.setStopped(vo.getStopped());
        }
        if (vo.getStopTime() != null) {
            entity.setStopTime(vo.getStopTime());
        }
        if (vo.getStopReason() != null) {
            entity.setStopReason(vo.getStopReason());
        }
        if (vo.getBusinessType() != null) {
            entity.setBusinessType(vo.getBusinessType());
        }
        if (vo.getRemark() != null) {
            entity.setRemark(vo.getRemark());
        }
        if (vo.getFileIds() != null) {
            entity.setFileIds(vo.getFileIds());
        }
    }

    private void copyToConfigWithNullCheck(ContractVo vo, BusinessContractConfigEntity config) {
        if (vo.getCustomerId() != null) {
            config.setCustomerId(vo.getCustomerId());
        }
        if (vo.getSupplierCorporationId() != null) {
            config.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getInvoiceTitle() != null) {
            config.setInvoiceTitle(vo.getInvoiceTitle());
        }
        if (vo.getInvoiceTaxNo() != null) {
            config.setInvoiceTaxNo(vo.getInvoiceTaxNo());
        }
        if (vo.getInvoiceBankName() != null) {
            config.setInvoiceBankName(vo.getInvoiceBankName());
        }
        if (vo.getInvoiceBankAccount() != null) {
            config.setInvoiceBankAccount(vo.getInvoiceBankAccount());
        }
        if (vo.getInvoiceRegisterAddress() != null) {
            config.setInvoiceRegisterAddress(vo.getInvoiceRegisterAddress());
        }
        if (vo.getInvoiceCompanyTel() != null) {
            config.setInvoiceCompanyTel(vo.getInvoiceCompanyTel());
        }
        if (vo.getInvoiceRemark() != null) {
            config.setInvoiceRemark(vo.getInvoiceRemark());
        }
        if (vo.getManageCalculationRule() != null) {
            if (ManageCalculationRuleEnum.EMPLOYEE_COUNT.name().equals(vo.getManageCalculationRule())) {
                config.setManageCalculationRule(ManageCalculationRuleEnum.EMPLOYEE_COUNT.name());
                config.setManageAmount(vo.getManageAmount()); // 此处假设amount与规则关联，非空时更新
                config.setManageRate(null);
            } else if (ManageCalculationRuleEnum.PAYABLE_AMOUNT_RATE.name().equalsIgnoreCase(vo.getManageCalculationRule())) {
                config.setManageCalculationRule(ManageCalculationRuleEnum.PAYABLE_AMOUNT_RATE.name());
                config.setManageRate(vo.getManageRate()); // 此处假设rate与规则关联，非空时更新
                config.setManageAmount(null);
            }
        }
    }

    public ContractVo queryContract(Long contractId) {
        BusinessContractEntity entity = getContractById(contractId);
        BusinessContractConfigEntity config = getConfigByContractId(contractId);
        ContractVo vo = new ContractVo();
        copyFromEntity(entity, vo);
        if (config != null) {
            copyFromConfig(config, vo);
        }
        vo.setCustomerName(customerManager.getCustomer(entity.getCustomerId()).getName());
        vo.setSupplierName(corporationManager.requireCorporation(entity.getSupplierCorporationId()).getName());
        vo.setSupplierId(entity.getSupplierId());
        return vo;
    }

    public List<ContractVo> queryContractBySupplier(Long supplierId) {

        if (supplierId == null) {
            return List.of();
        }

        QBusinessContractEntity contract = QBusinessContractEntity.businessContractEntity;
        QBusinessContractConfigEntity config = QBusinessContractConfigEntity.businessContractConfigEntity;

        List<Tuple> tuples = new JPAQueryFactory(em)
                .select(contract, config)
                .from(contract)
                .leftJoin(config).on(contract.id.eq(config.contractId))
                .where(contract.supplierId.eq(supplierId))
                .fetch();

        return tuples.stream()
                .map(tuple -> {
                    BusinessContractEntity contractEntity = tuple.get(contract);
                    BusinessContractConfigEntity configEntity = tuple.get(config);
                    ContractVo vo = new ContractVo();
                    copyFromEntity(contractEntity, vo);
                    if (configEntity != null) {
                        copyFromConfig(configEntity, vo);
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 提前终止合同
     */
    public void terminateContract(Long contractId, String stopReason, Long updaterId) {
        BusinessContractEntity entity = getContractById(contractId);
        if (entity == null) {
            throw new IllegalStateException("服务合同不存在");
        }

        // 检查合同是否已经终止
        if (Boolean.TRUE.equals(entity.getStopped())) {
            throw new IllegalStateException("合同已经终止，无法重复操作");
        }

        // 设置终止相关字段
        entity.setStopped(true);
        entity.setStopTime(Instant.now());
        entity.setStopReason(stopReason);
        entity.setUpdaterId(updaterId);
        entity.setStatus(BusinessContractStatusEnum.TERMINATION.name());

        em.merge(entity);
    }

    private BusinessContractEntity getContractById(Long id) {
        QBusinessContractEntity t = QBusinessContractEntity.businessContractEntity;
        return new JPAQueryFactory(em)
                .selectFrom(t)
                .where(t.id.eq(id))
                .fetchOne();
    }

    public BusinessContractConfigEntity getConfigByContractId(Long contractId) {
        QBusinessContractConfigEntity t = QBusinessContractConfigEntity.businessContractConfigEntity;
        return new JPAQueryFactory(em)
                .selectFrom(t)
                .where(t.contractId.eq(contractId))
                .fetchOne();
    }

    private void copyToEntity(ContractVo vo, BusinessContractEntity entity) {
        entity.setCustomerId(vo.getCustomerId());
        entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        entity.setName(StringUtils.defaultString(vo.getName()));
        entity.setSn(StringUtils.defaultIfBlank(vo.getSn(), generateSn()));
        entity.setTimeFixed(vo.getTimeFixed());
        entity.setStartDate(vo.getStartDate());
        entity.setEndDate(vo.getEndDate());
        entity.setStopped(vo.getStopped());
        entity.setStopTime(vo.getStopTime());
        entity.setStopReason(vo.getStopReason());
        entity.setBusinessType(vo.getBusinessType());
        entity.setRemark(vo.getRemark());
        entity.setFileIds(vo.getFileIds());
    }

    private void copyToConfig(ContractVo vo, BusinessContractConfigEntity config) {
        config.setCustomerId(vo.getCustomerId());
        config.setSupplierCorporationId(vo.getSupplierCorporationId());
        config.setInvoiceTitle(vo.getInvoiceTitle());
        config.setInvoiceTaxNo(vo.getInvoiceTaxNo());
        config.setInvoiceBankName(vo.getInvoiceBankName());
        config.setInvoiceBankAccount(vo.getInvoiceBankAccount());
        config.setInvoiceRegisterAddress(vo.getInvoiceRegisterAddress());
        config.setInvoiceCompanyTel(vo.getInvoiceCompanyTel());
        config.setInvoiceRemark(vo.getInvoiceRemark());
        if (ManageCalculationRuleEnum.EMPLOYEE_COUNT.name().equals(vo.getManageCalculationRule())) {
            config.setManageCalculationRule(ManageCalculationRuleEnum.EMPLOYEE_COUNT.name());
            config.setManageAmount(vo.getManageAmount());
            config.setManageRate(null);
        } else if (ManageCalculationRuleEnum.PAYABLE_AMOUNT_RATE.name().equalsIgnoreCase(vo.getManageCalculationRule())) {
            config.setManageCalculationRule(ManageCalculationRuleEnum.PAYABLE_AMOUNT_RATE.name());
            config.setManageRate(vo.getManageRate());
            config.setManageAmount(null);
        }

    }

    private void copyFromEntity(BusinessContractEntity entity, ContractVo vo) {
        vo.setId(entity.getId());
        vo.setCustomerId(entity.getCustomerId());
        vo.setSupplierCorporationId(entity.getSupplierCorporationId());
        vo.setName(entity.getName());
        vo.setSn(entity.getSn());
        vo.setTimeFixed(entity.getTimeFixed());
        vo.setStartDate(entity.getStartDate());
        vo.setEndDate(entity.getEndDate());
        vo.setStopped(entity.getStopped());
        vo.setStopTime(entity.getStopTime());
        vo.setStopReason(entity.getStopReason());
        vo.setBusinessType(entity.getBusinessType());
        vo.setRemark(entity.getRemark());
        vo.setFileIds(entity.getFileIds());
        vo.setStatus(entity.getStatus());
    }

    private void copyFromConfig(BusinessContractConfigEntity config, ContractVo vo) {
        vo.setInvoiceTitle(config.getInvoiceTitle());
        vo.setInvoiceTaxNo(config.getInvoiceTaxNo());
        vo.setInvoiceBankName(config.getInvoiceBankName());
        vo.setInvoiceBankAccount(config.getInvoiceBankAccount());
        vo.setInvoiceRegisterAddress(config.getInvoiceRegisterAddress());
        vo.setInvoiceCompanyTel(config.getInvoiceCompanyTel());
        vo.setInvoiceRemark(config.getInvoiceRemark());
        vo.setManageCalculationRule(config.getManageCalculationRule());
        vo.setManageAmount(config.getManageAmount());
        vo.setManageRate(config.getManageRate());
    }

    public JPAQuery<BusinessContractEntity> queryContract(Function<QBusinessContractEntity, Predicate> condition) {
        QBusinessContractEntity t = QBusinessContractEntity.businessContractEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private String generateSn() {
        return "CON-" + System.currentTimeMillis();
    }
}
