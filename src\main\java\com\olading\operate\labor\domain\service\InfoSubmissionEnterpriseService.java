package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionEnterpriseEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionEnterpriseManager;
import com.olading.operate.labor.domain.share.submission.QInfoSubmissionEnterpriseEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionEnterpriseVo;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class InfoSubmissionEnterpriseService {

    private final InfoSubmissionEnterpriseManager infoSubmissionEnterpriseManager;
    private final EntityManager entityManager;



    /**
     * 更新企业信息报送记录
     */
    public InfoSubmissionEnterpriseEntity updateInfoSubmissionEnterprise(TenantInfo tenantInfo, InfoSubmissionEnterpriseVo vo) {
        InfoSubmissionEnterpriseEntity entity = infoSubmissionEnterpriseManager.updateInfoSubmissionEnterprise(vo);
        return entity;
    }

    /**
     * 查询企业信息报送记录详情
     */
    public InfoSubmissionEnterpriseVo queryInfoSubmissionEnterprise(Long id) {
        InfoSubmissionEnterpriseVo vo = infoSubmissionEnterpriseManager.queryInfoSubmissionEnterprise(id);
        return vo;
    }

    /**
     * 删除企业信息报送记录
     */
    public void deleteInfoSubmissionEnterprise(TenantInfo tenantInfo, Long id) {
        // 删除记录
        infoSubmissionEnterpriseManager.deleteInfoSubmissionEnterprise(id);
    }

    /**
     * 根据作业主体ID查询企业信息报送记录列表（按ID倒序）
     */
    public List<InfoSubmissionEnterpriseEntity> queryInfoSubmissionEnterpriseBySupplierCorporationId(Long supplierCorporationId) {
        QInfoSubmissionEnterpriseEntity entity = QInfoSubmissionEnterpriseEntity.infoSubmissionEnterpriseEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.supplierCorporationId.eq(supplierCorporationId)
                        .and(entity.deleted.eq(false)))
                .orderBy(entity.id.desc())
                .fetch();
    }

    /**
     * 校验企业信息报送记录VO
     */
    private void validateInfoSubmissionEnterpriseVo(InfoSubmissionEnterpriseVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
        if (vo.getSupplierId() == null) {
            throw new BusinessException("灵工平台ID不能为空");
        }
    }
}
