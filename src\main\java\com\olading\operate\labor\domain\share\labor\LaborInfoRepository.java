package com.olading.operate.labor.domain.share.labor;

import com.olading.operate.labor.app.web.biz.labor.vo.LaborBasicVo;
import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.QLaborProtocolEntity;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Transactional
@Slf4j
@Component
public class LaborInfoRepository {

    private final EntityManager em;

    public void saveLaborProtocol(LaborProtocolEntity laborProtocolEntity) {
        em.persist(laborProtocolEntity);
    }

    public void updateLaborProtocol(LaborProtocolEntity laborProtocolEntity) {
        em.merge(laborProtocolEntity);
    }


    public SupplierLaborEntity findLaborByIdCard(String idCard, Long supplierId) {
        QSupplierLaborEntity labor = QSupplierLaborEntity.supplierLaborEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.supplierId.eq(supplierId)
                        .and(labor.idCard.eq(idCard)))
//                        .and(labor.deleted.eq("0")))
                .fetchOne();
    }


    public SupplierLaborEntity findUniqueLaborByIdCard(
            Long supplierCorporationId,
            String idCard,
            String name
    ) {
        QSupplierLaborEntity labor = QSupplierLaborEntity.supplierLaborEntity;
        QLaborInfoEntity laborInfo = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                // 关联 LaborInfoEntity 表
                .innerJoin(laborInfo)
                .on(labor.id.eq(laborInfo.laborId))
                // 添加查询条件
                .where(
                        laborInfo.supplierCorporationId.eq(supplierCorporationId)
                                .and(labor.idCard.eq(idCard))
                                .and(labor.name.eq(name))
                                .and(labor.deleted.eq(false))
                )
                .fetchOne();

    }

    public LaborInfoEntity findLaborById(Long id) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.id.eq(id)
                        .and(labor.deleted.eq(false)))
                .fetchOne();
    }

    public List<SupplierLaborEntity> findLaborsByIdCard(Long supplierId, List<String> cards) {
        QSupplierLaborEntity labor = QSupplierLaborEntity.supplierLaborEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.supplierId.eq(supplierId).and(labor.idCard.in(cards))
                        .and(labor.deleted.eq(false)))
                .fetch();
    }

    public LaborInfoEntity findLaborInfoByContract(Long supplierId, Long laborId, Long contractId) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.supplierId.eq(supplierId)
                        .and(labor.laborId.eq(laborId))
                        .and(labor.contractId.eq(contractId)
                                .and(labor.deleted.eq(false))))
                .fetchOne();
    }
    public LaborInfoEntity findLaborInfoByCorporationAndContract(Long supplierCorporationId, Long laborId, Long contractId) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.supplierCorporationId.eq(supplierCorporationId)
                        .and(labor.laborId.eq(laborId))
                        .and(labor.contractId.eq(contractId)
                                .and(labor.deleted.eq(false))))
                .fetchOne();
    }

    public SupplierLaborVo findLaborInfoByContract(Long contractId, String idCard) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        QSupplierLaborEntity supplierLabor = QSupplierLaborEntity.supplierLaborEntity;
        final Tuple tuple = new JPAQueryFactory(em)
                .select(labor, supplierLabor).distinct()
                .from(labor)
                .leftJoin(supplierLabor).on(labor.laborId.eq(supplierLabor.id))
                .where(supplierLabor.idCard.eq(idCard).and(labor.contractId.eq(contractId)
                        .and(labor.deleted.eq(false))))
                .fetchOne();
        if (tuple == null || tuple.get(labor) == null || tuple.get(supplierLabor) == null) {
            return null;
        }
        return SupplierLaborVo.builder(tuple.get(labor), tuple.get(supplierLabor));
    }

    public List<LaborInfoEntity> findLaborListByContract(Long supplierId, List<Long> contractIds) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.supplierId.eq(supplierId)
                        .and(labor.contractId.in(contractIds)
                                .and(labor.deleted.eq(false))))
                .fetch();
    }


    // 查询方法：根据ID查询劳务信息
    public LaborInfoEntity findById(Long id) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.id.eq(id))
                .fetchOne();
    }

    // 新增方法：根据 LaborInfoEntity 的 id 查询并连接 SupplierLaborEntity
    public SupplierLaborVo findLaborInfoWithSupplierById(Long id, Long supplierId) {
        QLaborInfoEntity laborInfo = QLaborInfoEntity.laborInfoEntity;
        QSupplierLaborEntity supplierLabor = QSupplierLaborEntity.supplierLaborEntity;

        Tuple tuple = new JPAQueryFactory(em)
                .select(laborInfo, supplierLabor)
                .from(laborInfo)
                .leftJoin(supplierLabor).on(laborInfo.laborId.eq(supplierLabor.id))
                .where(laborInfo.id.eq(id).and(supplierLabor.supplierId.eq(supplierId)))
                .fetchOne();
        if (tuple == null || tuple.get(laborInfo) == null || tuple.get(supplierLabor) == null) {
            throw new ApiException("劳务信息不存在", ApiException.SYSTEM_ERROR);
        }
        return SupplierLaborVo.builder(tuple.get(laborInfo), tuple.get(supplierLabor));
    }


    public List<LaborBasicVo> findLaborByContract(List<Long> contractIds, Long supplierId) {
        QLaborInfoEntity laborInfo = QLaborInfoEntity.laborInfoEntity;
        QSupplierLaborEntity supplierLabor = QSupplierLaborEntity.supplierLaborEntity;

        List<Tuple> tuples = new JPAQueryFactory(em)
                .select(laborInfo.contractId, supplierLabor.idCard, supplierLabor.cellphone, supplierLabor.name)
                .from(laborInfo)
                .leftJoin(supplierLabor).on(laborInfo.laborId.eq(supplierLabor.id))
                .where(laborInfo.id.in(contractIds).and(supplierLabor.supplierId.eq(supplierId)))
                .fetch();

        return tuples.stream().map(tuple -> LaborBasicVo.builder(tuple.get(laborInfo.contractId),
                tuple.get(supplierLabor.idCard),
                tuple.get(supplierLabor.cellphone),
                tuple.get(supplierLabor.name))).collect(Collectors.toList());
    }

    public List<LaborInfoEntity> findLaborInfoByIds(List<Long> laborInfoIds) {
        QLaborInfoEntity laborInfo = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(em)
                .select(laborInfo)
                .from(laborInfo)
                .where(laborInfo.id.in(laborInfoIds))
                .fetch();
    }

    public List<SupplierLaborEntity> findLaborByIds(List<Long> laborIds) {
        QSupplierLaborEntity labor = QSupplierLaborEntity.supplierLaborEntity;
        return new JPAQueryFactory(em)
                .select(labor)
                .from(labor)
                .where(labor.id.in(laborIds))
                .fetch();
    }

    // 新增方法：保存劳务信息
    public void saveLaborInfo(LaborInfoEntity entity) {
        em.persist(entity);
    }

    public void saveLabor(SupplierLaborEntity entity) {
        em.persist(entity);
    }

    // 修改方法：更新劳务信息
    public void updateLaborInfo(LaborInfoEntity entity) {
        em.merge(entity);
    }
    public void updateSupplierLaborInfo(SupplierLaborEntity entity) {
        em.merge(entity);
    }

    // 删除方法：根据ID删除劳务信息
    public void deleteLaborInfoById(Long id) {
        QLaborInfoEntity labor = QLaborInfoEntity.laborInfoEntity;
        new JPAQueryFactory(em).delete(labor)
                .where(labor.id.eq(id))
                .execute();
    }

    public List<LaborProtocolEntity> findLaborProtocolByCardAndCorporationId(List<String> cards, Long corporationId) {
        QLaborProtocolEntity laborProtocol = QLaborProtocolEntity.laborProtocolEntity;
        return new JPAQueryFactory(em)
                .select(laborProtocol)
                .from(laborProtocol)
                .where(laborProtocol.idCard.in(cards).and(laborProtocol.supplierCorporationId.eq(corporationId)))
                .fetch();
    }
}