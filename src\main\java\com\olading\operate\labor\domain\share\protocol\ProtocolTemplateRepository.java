package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.app.web.biz.enums.ProtocolTempEnum;
import com.olading.operate.labor.domain.ApiException;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

@RequiredArgsConstructor
@Transactional
@Slf4j
@Component
public class ProtocolTemplateRepository {

    private final EntityManager em;

    public List<CorporationProtocolTemplateEntity> queryTemplateByStatus(List<ProtocolTempEnum> status, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> list = status.stream().map(ProtocolTempEnum::name).toList();
        return queryProtocolTemplate(entity -> entity.status.in(list).and(entity.modifyTime.between(startTime, endTime))).fetch();
    }

    public void saveTemplateFields(List<CorporationProtocolTempFiledEntity> list) {
        list.forEach(em::persist);
    }
    public void saveTemplateField(CorporationProtocolTempFiledEntity entity) {
        em.persist(entity);
    }

    public void saveTemplateSteps(List<CorporationProtocolTempStepEntity> list) {
        list.forEach(em::persist);
    }
     public void saveTemplateStep(CorporationProtocolTempStepEntity step) {
        em.persist(step);
    }

    public void updateTemplateSteps(CorporationProtocolTempStepEntity entity) {
        em.merge(entity);
    }

    public void saveTemplateRangeList(List<CorporationProtocolTempRangeEntity> list) {
        list.forEach(em::persist);
    }

    public void deleteRangeByTemp(Long tempId) {
        QCorporationProtocolTempRangeEntity range = QCorporationProtocolTempRangeEntity.corporationProtocolTempRangeEntity;
        new JPAQueryFactory(em).delete(range)
                .where(range.templateId.eq(tempId))
                .execute();
    }

    public void removeFieldsByTemp(Long tempId) {
        QCorporationProtocolTempFiledEntity filed = QCorporationProtocolTempFiledEntity.corporationProtocolTempFiledEntity;
        new JPAQueryFactory(em).delete(filed)
                .where(filed.templateId.eq(tempId))
                .execute();
    }

    public void addProtocolTemplate(CorporationProtocolTemplateEntity entity) {
        em.persist(entity);
    }

    public void updateProtocolTemp(CorporationProtocolTemplateEntity entity) {
        em.merge(entity);
    }

    public void updateProtocolTempStatus(Long id, ProtocolTempEnum tempStatus) {
        CorporationProtocolTemplateEntity en = queryProtocolTempById(id);
        if (en == null) {
            throw new ApiException("修改的模板信息不存在", ApiException.API_PARAM_ERROR);
        }
        if (tempStatus == ProtocolTempEnum.ENABLED && ProtocolTempEnum.ENABLED.name().equals(en.getStatus())) {
            throw new ApiException("模板信息已变更",  ApiException.API_PARAM_ERROR);
        }
        if (tempStatus == ProtocolTempEnum.DISABLED && ProtocolTempEnum.DISABLED.name().equals(en.getStatus())) {
            throw new ApiException("模板信息已变更",  ApiException.API_PARAM_ERROR);
        }
        en.setId(id);
        en.setStatus(tempStatus.name());
        em.merge(en);
    }

    public CorporationProtocolTemplateEntity queryProtocolTempById(Long id) {
        return queryProtocolTemplate(t -> t.id.eq(id)).fetchOne();
    }


    public List<CorporationProtocolTemplateEntity> queryProtocolTempByName(String tempName, Long supplierId) {
        return queryProtocolTemplate(t -> t.supplierId.eq(supplierId)
                .and(t.tempName.eq(tempName))).fetch();
    }

    public List<CorporationProtocolTempFiledEntity> queryDefaultProtocolTempFiled() {
        return queryProtocolTempFiled(v -> v.supplierId.eq(0L).and(v.tenantId.eq("0"))).fetch();
    }

    public List<CorporationProtocolTempFiledEntity> queryTempFiledByTempId(Long supplierId, Long tempId) {
        return queryProtocolTempFiled(v -> v.supplierId.eq(supplierId).and(v.templateId.eq(tempId))).fetch();
    }

    public List<CorporationProtocolTempRangeEntity> queryTempRangeByTempId(Long tempId) {
        QCorporationProtocolTempRangeEntity range = QCorporationProtocolTempRangeEntity.corporationProtocolTempRangeEntity;
        return new JPAQueryFactory(em).select(range)
                .from(range)
                .where(range.templateId.eq(tempId))
                .fetch();
    }

    public List<CorporationProtocolTempStepEntity> queryTempStepByTempId(Long tempId) {
        QCorporationProtocolTempStepEntity step = QCorporationProtocolTempStepEntity.corporationProtocolTempStepEntity;
        return new JPAQueryFactory(em).select(step)
                .from(step)
                .where(step.templateId.eq(tempId))
                .orderBy(step.sortBy.asc())
                .fetch();
    }

    public List<CorporationProtocolTempStepEntity> queryTempFiledByIds(List<Long> ids) {
        return queryProtocolTempStep(v -> v.supplierId.in(ids)).fetch();
    }

    public List<CorporationProtocolTempFiledEntity> queryTempFiledByStep(Long stepId) {
        return queryProtocolTempFiled(v -> v.templateStepId.in(stepId)).fetch();
    }

    public List<CorporationProtocolTempFiledEntity> queryTempFiledBySteps(List<Long> stepIds) {
        return queryProtocolTempFiled(v -> v.templateStepId.in(stepIds)).fetch();
    }



    private JPAQuery<CorporationProtocolTemplateEntity> queryProtocolTemplate(Function<QCorporationProtocolTemplateEntity, Predicate> condition) {
        QCorporationProtocolTemplateEntity t = QCorporationProtocolTemplateEntity.corporationProtocolTemplateEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<CorporationProtocolTempFiledEntity> queryProtocolTempFiled(Function<QCorporationProtocolTempFiledEntity, Predicate> condition) {
        QCorporationProtocolTempFiledEntity t = QCorporationProtocolTempFiledEntity.corporationProtocolTempFiledEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<CorporationProtocolTempStepEntity> queryProtocolTempStep(Function<QCorporationProtocolTempStepEntity, Predicate> condition) {
        QCorporationProtocolTempStepEntity t = QCorporationProtocolTempStepEntity.corporationProtocolTempStepEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

}
