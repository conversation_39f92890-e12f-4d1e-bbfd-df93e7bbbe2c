package com.olading.operate.labor.app.web.biz.salary;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.query.WebApiQueryService;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.EnumTaxCalculationMethod;
import com.olading.operate.labor.app.web.biz.labor.vo.ImportLaborRow;
import com.olading.operate.labor.app.web.biz.salary.vo.ImportPayrollStaffRow;
import com.olading.operate.labor.app.web.biz.salary.vo.ImportPreviousIncomeRow;
import com.olading.operate.labor.app.web.biz.salary.vo.PayrollCheckFailIData;
import com.olading.operate.labor.app.web.biz.salary.vo.PreviousIncomeImportFailData;
import com.olading.operate.labor.app.web.biz.salary.vo.PreviousIncomeImportRequest;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.PreviousIncomeDeductionQuery;
import com.olading.operate.labor.domain.query.SalaryDetailQuery;
import com.olading.operate.labor.domain.query.SalaryQuery;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.salary.vo.PreviousIncomeDeductionVO;
import com.olading.operate.labor.domain.salary.vo.SalaryVO;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SalaryCalculateService;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.RedisUtils;
import com.olading.operate.labor.util.excel.ExcelColumnEntity;
import com.olading.operate.labor.util.excel.ExcelHeader;
import com.olading.operate.labor.util.excel.ExcelResult;
import com.olading.operate.labor.util.excel.ExcelType;
import com.olading.operate.labor.util.excel.Excels;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@Tag(name = "薪酬计算相关接口")
@RestController
@RequestMapping("/api/supplier/salary")
@RequiredArgsConstructor
@Slf4j
public class SalaryCalculateController extends BusinessController {
    private final SalaryManager salaryManager;
    private final QueryService queryService;
    private final WebApiQueryService webApiQueryService;
    private final SalaryCalculateService salaryCalculateService;
    private final BusinessContractManager businessContractManager;
    private final RedisUtils redisUtils;



    private static final String CONTENT_TYPE = "application/vnd.ms-excel";
    private static final String CONTENT_DISPOSITION = "attachment;filename=";
    private static final String SYSTEM_CALCULATION_TEMPLATE = "/template/上传工资表模版-系统预算.xlsx";
    private static final String PREVIOUS_INCOME_IMPORT_TEMPLATE = "/template/上期收入减除导入模版.xlsx";

    @Operation(summary = "薪酬计算-列表")
    @PostMapping(value = "listPayroll")
    public WebApiQueryResponse<SalaryVO> listSalary(@RequestBody QueryFilter<WebSalaryFilters> request) {
        QueryFilter<SalaryQuery.Filters> filter = request.convert(WebSalaryFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        filter.setWithDeleted(false);
        DataSet<SalaryVO> ds = queryService.querySalary(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "薪酬计算-确认算税结果")
    @PostMapping(value = "confirmPayroll")
    public WebApiResponse<Void> confirmSalary(@RequestBody SalaryParams request) {
        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(request.getId());
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        if (salaryStatementEntity.getStatus() != SalaryStatementStatus.UNCONFIRMED) {
            throw ApiException.apiError("工资表状态不正确，无法确认");
        }
        salaryStatementEntity.setStatus(SalaryStatementStatus.CONFIRMED);
        salaryManager.updateSalaryStatement(salaryStatementEntity);
        return WebApiResponse.success();
    }

    @Operation(summary = "薪酬计算-删除")
    @PostMapping(value = "deletePayroll")
    public WebApiResponse<Void> deleteSalary(@RequestBody SalaryParams request) {
        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(request.getId());
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        if (salaryStatementEntity.getStatus() == SalaryStatementStatus.CONFIRMED) {
            throw ApiException.apiError("工资表已确认，无法删除");
        }
        salaryManager.deleteSalaryDetailBySalaryStatementId(salaryStatementEntity.getId());
        salaryManager.deleteSalaryStatement(salaryStatementEntity);
        return WebApiResponse.success();
    }

    @Operation(summary = "薪酬计算-查看")
    @PostMapping(value = "listPayrollDetail")
    public WebApiQueryResponse<SalaryDetailEntity> listSalaryDetail(@RequestBody SalaryParams request) {
        List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailByStatementId(request.getId());
        return WebApiQueryResponse.success(salaryDetails, salaryDetails.size());
    }


    @Operation(summary = "薪酬计算-下载")
    @PostMapping(value = "payRollDetailDownload")
    public void payRollDetailDownload(@RequestBody SalaryParams request, HttpServletResponse response) {
        List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailByStatementId(request.getId());
        QueryFilter<SalaryDetailQuery.Filters> filter = new QueryFilter<>();
        filter.getFilters().setSupplierCorporationId(request.getId());
        filter.sort("id", Direction.DESCENDING);
        downloadExcel(response,
                "业务主体列表.zip",
                webApiQueryService.exportSalaryDetail(),
                filter);
    }

    @Operation(summary = "薪酬计算-上期收入减除导入")
    @PostMapping(value = "listPreviousIncomeDeduction")
    public WebApiQueryResponse<PreviousIncomeDeductionVO> listPreviousIncomeDeduction(@RequestBody QueryFilter<WebPreviousIncomeDeductionFilters> request) {
        QueryFilter<PreviousIncomeDeductionQuery.Filters> filter = request.convert(WebPreviousIncomeDeductionFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        filter.setWithDeleted(false);
        DataSet<PreviousIncomeDeductionVO> ds = queryService.queryPreviousIncomeDeduction(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }
    

    @Operation(summary = "应发模板下载")
    @PostMapping(value = "download/template")
    public void downloadTemplate(
            @Parameter(description = "算税方式") @RequestParam String taxCalculationMethod,
            HttpServletResponse response) {

        if (taxCalculationMethod == null) {
            throw new ApiException(ApiException.API_PARAM_ERROR, "算税方式不能为空");
        }

        // 根据算税方式选择模板路径
        String templatePath = (taxCalculationMethod.equals( EnumTaxCalculationMethod.SYSTEM.name()))
                ? SYSTEM_CALCULATION_TEMPLATE
                : PREVIOUS_INCOME_IMPORT_TEMPLATE;

        try {
            // 统一处理Excel下载
            downloadExcel(templatePath, response);
        } catch (IOException e) {
            log.error("批量导入人员模板下载失败 | 算税方式: {} | 错误信息: {}", taxCalculationMethod, e.getMessage(), e);
            throw new ApiException("批量导入人员模板下载失败", ApiException.SYSTEM_ERROR);
        }
    }

    /**
     * 通用的Excel下载方法
     */
    private void downloadExcel(String templatePath, HttpServletResponse response) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(new DefaultResourceLoader().getResource(templatePath).getInputStream())) {
        String TEMPLATE_FILE_NAME = templatePath.substring(templatePath.lastIndexOf("/") + 1);
            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", CONTENT_TYPE);
            response.setHeader("Content-Disposition",CONTENT_DISPOSITION + URLEncoder.encode(TEMPLATE_FILE_NAME, "UTF-8"));

            // 写入输出流
            workbook.write(response.getOutputStream());
        }
    }

    @Operation(summary = "薪酬管理-新增工资表")
    @PostMapping(value = "addPayroll")
    @ResponseBody
    public WebApiResponse<UploadResult> batchUploadCheck(@RequestPart("file")MultipartFile file,@Schema(description = "合同ID") @RequestParam @NotNull(message = "服务合同不能为空") Long contractId, @Schema(description = "税款所属期") @RequestParam @NotNull(message = "税款所属期不能为空") String taxPeriod) {


        ContractVo contractVo = businessContractManager.queryContract(contractId);
        payrollAddRequest request = new payrollAddRequest();
        request.setCustomerId(contractVo.getCustomerId());
        request.setSupplierCorporationId(contractVo.getSupplierCorporationId());
        request.setContractId(contractId);
        request.setTaxPeriod(taxPeriod);


        Long supplierId = currentSupplier().getId();
        request.setSupplierId(supplierId);
        Map<OwnerType, Set<Long>> ownerTypeSetMap = currentDataScope();
        TenantInfo tenant = currentTenant();


        //当前作业主体当前税款所属期只能有一条非已确认状态的数据
        salaryCalculateService.verifyPayrollStaff(request);

        if (file.getSize() > 5 * 1024 * 1024) {
            throw new ApiException(ApiException.API_PARAM_ERROR, "文件大小不能超过5M");
        }
        ExcelResult excelResult = null;
        try {
            excelResult = Excels.reader(ImportPayrollStaffRow.class)
                    .maxDataRows(5000)
                    .needVerify(true)
                    .build()
                    .read(file.getInputStream());
        }  catch (Exception e) {
            throw new ApiException("读取Excel文件错误", ApiException.SYSTEM_ERROR);
        }
        if (excelResult.isResultEmpty()) {
            throw new ApiException("模板中无数据,请导入数据！", ApiException.API_PARAM_ERROR);
        }
        salaryCalculateService.verifyImportPayrollStaffRow(request, excelResult.getResults(), ownerTypeSetMap);

        List<List<Object>> successList = new ArrayList<>();
        List<ImportPayrollStaffRow> successDataList = new ArrayList<>();
        List<PayrollCheckFailIData> failList = new ArrayList<>();

        excelResult.getResults().forEach(i -> {
            ImportPayrollStaffRow row = (ImportPayrollStaffRow) i;
            if (row.isVerifyFail()) {
                final PayrollCheckFailIData bean = BeanUtil.toBean(row, PayrollCheckFailIData.class);
                bean.setErrorMsg(row.getRowErrorString());
                failList.add(bean);
            } else {
                successDataList.add(row);
            }
        });

        UploadResult result = new UploadResult();

        if (!successDataList.isEmpty() && failList.isEmpty()) {
            salaryCalculateService.addPayrollStaff(request, successDataList, tenant);
        }else {
            String key = UUID.randomUUID().toString().replaceAll("-", "");
            redisUtils.set(key + "_fail", failList, 300);
            redisUtils.set(key + "_header", excelResult.getHeaderTitles(), 300);
            result.setUuid(key);
        }




        result.setSuccessCount(successList.size());
        result.setFailCount(failList.size());

        return WebApiResponse.success(result);
    }

    @Operation(summary = "错误日志下载")
    @PostMapping(value = "/importVerifyErrorLog/{uuid}")
    public void downloadDataAuthErrorRecord(HttpServletRequest request, HttpServletResponse response, @PathVariable String uuid) throws IOException {

        List<ImportLaborRow> failList = redisUtils.get(uuid + "_fail", new TypeReference<>() {
        });
        List<String> headerList = redisUtils.get(uuid + "_header", new TypeReference<>() {
        });
        if (StringUtils.isBlank(uuid) || CollectionUtils.isEmpty(failList)) {
            throw new ApiException("文件已经失效，请重新上传文件进行下载！", ApiException.API_PARAM_ERROR);
        }
        headerList.add("反馈信息");
        List<ExcelColumnEntity> headers = new ArrayList<>();
        int colIndex = 0;
        for (String name : headerList) {
            ExcelColumnEntity column = new ExcelColumnEntity();
            column.setExcelTitle(name);
            column.setColIndex(colIndex++);
            headers.add(column);
        }

        ExcelResult excelResult = new ExcelResult();
        excelResult.setExcelHeader(new ExcelHeader(headers));
        failList.stream().forEach(excelResult::addRow);
        Workbook hssfWorkbook = new HSSFWorkbook(new DefaultResourceLoader().getResource(SYSTEM_CALCULATION_TEMPLATE).getInputStream());
        Workbook workbook = Excels.writer(excelResult)
                .sheetName("批量导入模板")
                .rowHeight(30)
                .excelType(ExcelType.XSSF)
                .writeToWorkbook(hssfWorkbook);

        String fileName = String.format("%s_%s.xlsx", StringUtils.join("错误文件", "_"),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        workbook.write(response.getOutputStream());
    }


    @Operation(summary = "上期收入减除导入")
    @PostMapping(value = "previousIncomeImport")
    public WebApiResponse<UploadResult> previousIncomeImport(@RequestPart("file") MultipartFile file,@RequestParam @NotNull(message = "作业主体不能为空") Long supplierCorporationId, @Schema(description = "税款所属期")@RequestParam @NotNull(message = "税款所属期不能为空") String taxPeriod) {

        PreviousIncomeImportRequest request = new PreviousIncomeImportRequest();
        request.setSupplierCorporationId(supplierCorporationId);
        request.setTaxPeriod(taxPeriod);

        request.setSupplierId(currentSupplierId());
        TenantInfo tenant = currentTenant();
        Map<OwnerType, Set<Long>> ownerTypeSetMap = currentDataScope();

        //导入上月收入与减除时，当月工资表不能有数据，且每个月只能导入一次
        salaryCalculateService.verifyCurrentMonthPayrollStaff(request);


        if (file.getSize() > 5 * 1024 * 1024) {
            throw new ApiException(ApiException.API_PARAM_ERROR, "文件大小不能超过5M");
        }
        ExcelResult excelResult = null;
        try {
            excelResult = Excels.reader(ImportPreviousIncomeRow.class)
                    .maxDataRows(5000)
                    .needVerify(true)
                    .build()
                    .read(file.getInputStream());
        }  catch (Exception e) {
            throw new ApiException("读取Excel文件错误", ApiException.SYSTEM_ERROR);
        }
        if (excelResult.isResultEmpty()) {
            throw new ApiException("模板中无数据,请导入数据！", ApiException.API_PARAM_ERROR);
        }

        salaryCalculateService.verifyPreviousIncomeInfo(request, excelResult.getResults(), ownerTypeSetMap);

        List<List<Object>> successList = new ArrayList<>();
        List<ImportPreviousIncomeRow> successDataList = new ArrayList<>();
        List<PreviousIncomeImportFailData> failList = new ArrayList<>();

        excelResult.getResults().forEach(i -> {
            ImportPreviousIncomeRow row = (ImportPreviousIncomeRow) i;
            if (row.isVerifyFail()) {
                PreviousIncomeImportFailData failData = BeanUtil.toBean(row, PreviousIncomeImportFailData.class);
                failData.setErrorMsg(row.getRowErrorString());
                failList.add(failData);
            } else {
                successDataList.add(row);
            }
        });

        if (!successDataList.isEmpty() && failList.isEmpty()) {
            salaryCalculateService.addPreviousIncomeStaff(request, successDataList, tenant);
        }

        String key = UUID.randomUUID().toString().replaceAll("-", "");
        redisUtils.set(key + "_fail", failList, 300);
        redisUtils.set(key + "_header", excelResult.getHeaderTitles(), 300);

        UploadResult result = new UploadResult();
        result.setSuccessCount(successList.size());
        result.setFailCount(failList.size());
        result.setUuid(key);
        return WebApiResponse.success(result);
    }


    @Data
    public static class WebSalaryFilters {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "合同id")
        private Long contractId;

        @Schema(description = "合同名称")
        private String contractName;

        @Schema(description = "客户id")
        private List<Long> customerId;

        @Schema(description = "客户名称")
        private String customerName;

        @Schema(description = "作业主体id")
        private Long supplierCorporationId;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        @Schema(description = "工资表状态")
        private String status;

        public SalaryQuery.Filters convert() {
            return Beans.copyBean(this, SalaryQuery.Filters.class);
        }
    }

    @Data
    public static class WebPreviousIncomeDeductionFilters {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "作业主体id")
        private Long supplierCorporationId;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        public PreviousIncomeDeductionQuery.Filters convert() {
            return Beans.copyBean(this, PreviousIncomeDeductionQuery.Filters.class);
        }
    }

    @Data
    public static class SalaryParams {

        @NotNull(message = "工资表ID不能为空")
        @Schema(description = "工资表ID")
        private Long id;

    }


    @Getter
    @Setter
    public static class UploadResult {
        @Schema(description = "成功条数")
        private Integer successCount;
        @Schema(description = "失败条数")
        private Integer failCount;
        @Schema(description = "key")
        private String uuid;
    }

    @Data
    public static class payrollAddRequest {

        @Schema(description = "客户ID")
        @NotNull(message = "客户不能为空")
        private Long customerId;

        @Schema(description = "作业主体ID")
        @NotNull(message = "作业主体不能为空")
        private Long supplierCorporationId;

        @Schema(description = "合同ID")
        @NotNull(message = "服务合同不能为空")
        private Long contractId;

        @Schema(description = "税款所属期")
        @NotBlank(message = "税款所属期不能为空")
        @Pattern(
                regexp = "^\\d{4}-(0[1-9]|1[0-2])$",  // yyyy-MM 格式正则
                message = "税款所属期格式必须为 yyyy-MM（如 2023-05）"
        )
        private String taxPeriod;

        @Schema(description = "申报月")
        private String reportMonth;

        @Schema(description = "服务商ID")
        private Long supplierId;


        @Schema(description = "算税方式")
        private EnumTaxCalculationMethod taxCalculationMethod;
    }


}
