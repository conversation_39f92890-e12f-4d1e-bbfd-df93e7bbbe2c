package com.olading.operate.labor.domain.proxy.order;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@Comment("工资代发订单")
@Entity
@Table(name = "t_proxy_order")
public class ProxyOrderEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("客户id")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("合同id")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @NotNull
    @Comment("代发批次id")
    @Column(name = "proxy_batch_id", nullable = false)
    private Long proxyBatchId;

    @NotNull
    @Comment("应发工资id")
    @Column(name = "salary_detail_id", nullable = false)
    private Long salaryDetailId;

    @Size(max = 20)
    @NotNull
    @Comment("姓名")
    @Column(name = "name", nullable = false, length = 20)
    private String name;

    @Size(max = 20)
    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 20)
    private String idCard;


    @Comment("手机号")
    @Column(name = "cellphone", length = 11)
    private String cellphone;


    @Comment("银行卡号")
    @Column(name = "bank_card", length = 24)
    private String bankCard;

    @Comment("金额")
    @Column(name = "amount", precision = 19, scale = 2)
    private BigDecimal amount;

    @Size(max = 64)
    @NotNull
    @Comment("通道编码")
    @Column(name = "pay_channel", nullable = false, length = 64)
    private String payChannel;

    @Comment("完成时间")
    @Column(name = "complete_time")
    private LocalDateTime completeTime;

    @Size(max = 64)
    @Comment("错误码")
    @Column(name = "error_code", length = 64)
    private String errorCode;

    @Size(max = 256)
    @Comment("错误信息")
    @Column(name = "last_error_info", length = 256)
    private String lastErrorInfo;


    @NotNull
    @Comment("状态 CHECK_SUCC/CHECK_FAIL/PROCESSING/REMIT/FAIL")
    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private ProxyOrderStatusEnum status;

    @Size(max = 256)
    @Comment("备注")
    @Column(name = "remark", length = 256)
    private String remark;

}