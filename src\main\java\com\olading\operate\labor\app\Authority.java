package com.olading.operate.labor.app;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public final class Authority {

    public static final String SYS_BOSS = "sys.boss";

    public static final String SYS_SUPPLIER = "sys.supplier";

    public static final String SYS_CUSTOMER = "sys.customer";

    public static final String SYS_PERSONAL = "sys.personal";

    /**
     * 平台-设置-信息配置
     */
    public static final String SUPPLIER_SETTING_PROFILE = "supplier.setting.profile";
    /**
     * 平台-设置-角色权限管理
     */
    public static final String SUPPLIER_SETTING_ROLE = "supplier.setting.role";
    /**
     * 平台-设置-作业主体管理
     */
    public static final String SUPPLIER_SETTING_CORPORATION = "supplier.setting.corporation";
    /**
     * 平台-客户-客户信息
     */
    public static final String SUPPLIER_CUSTOMER_INFO = "supplier.customer.info";
    /**
     * 平台-客户-服务合同
     */
    public static final String SUPPLIER_CUSTOMER_CONTRACT = "supplier.customer.contract";
    /**
     * 平台-人员-人员信息
     */
    public static final String SUPPLIER_LABOR_INFO = "supplier.labor.info";
    /**
     * 平台-人员-服务合同-电子合同
     */
    public static final String SUPPLIER_LABOR_CONTRACT_ELEC = "supplier.labor.contract.elec";
    /**
     * 平台-人员-服务合同-合同模版
     */
    public static final String SUPPLIER_LABOR_CONTRACT_TMPL = "supplier.labor.contract.tmpl";
    /**
     * 平台-结算-薪酬管理
     */
    public static final String SUPPLIER_STATEMENT_SALARY = "supplier.statement.salary";
    /**
     * 平台-结算-账单管理
     */
    public static final String SUPPLIER_STATEMENT_BILL = "supplier.statement.bill";
    /**
     * 平台-人结算-发票管理
     */
    public static final String SUPPLIER_STATEMENT_INVOICE = "supplier.statement.invoice";
    /**
     * 平台-税务-信息报送-人员信息报送
     */
    public static final String SUPPLIER_TAX_SUBMISSION_LABOR = "supplier.tax.submission.labour";
    /**
     * 平台-税务-信息报送-人员收入信息报送
     */
    public static final String SUPPLIER_TAX_SUBMISSION_INCOME = "supplier.tax.submission.income";
    /**
     * 平台-税务-信息报送-企业信息报送
     */
    public static final String SUPPLIER_TAX_SUBMISSION_CUSTOMER = "supplier.tax.submission.customer";
    /**
     * 平台-税务-个税申报
     */
    public static final String SUPPLIER_TAX_DECLARE = "supplier.tax.declare";
    /**
     * 平台-税务-税款缴纳
     */
    public static final String SUPPLIER_TAX_PAY = "supplier.tax.pay";
    /**
     * 平台-税务-增值税申报
     */
    public static final String SUPPLIER_TAX_SURTAX = "supplier.tax.surtax";



    /**
     * 客户订单
     */
    public static final String CUSTOMER_ORDER = "customer.order";

    /**
     * 客户账户
     */
    public static final String CUSTOMER_ACCOUNT = "customer.account";

    /**
     * 客户信息
     */
    public static final String CUSTOMER_INFORMATION = "customer.information";

    /**
     * 客户任务
     */
    public static final String CUSTOMER_TASK = "customer.task";


    private static final Set<String> supplierAuthorities;


    public static final Set<String> CUSTOMER_AUTHORITIES = Set.of(
            SYS_CUSTOMER,
            CUSTOMER_ORDER,
            CUSTOMER_ACCOUNT,
            CUSTOMER_TASK
    );

    static {
        try {
            supplierAuthorities = Set.copyOf(find("SUPPLIER_"));
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> find(String prefix) throws IllegalAccessException {

        List<String> list = new ArrayList<>();
        for (Field field : Authority.class.getDeclaredFields()) {
            if (Modifier.isStatic(field.getModifiers())
                    && Modifier.isFinal(field.getModifiers())
                    && Modifier.isPublic(field.getModifiers())
                    && field.getType() == String.class
                    && field.getName().startsWith(prefix)) {
                list.add((String) field.get(null));
            }
        }
        return list;

    }

    /**
     * 获取资质地系统的全部权限
     */
    public static Set<String> getSupplierAllAuthorities() {
        return supplierAuthorities;
    }

    public static Set<String> getAllCustomerAuthorities() {
        return CUSTOMER_AUTHORITIES;
    }
}
