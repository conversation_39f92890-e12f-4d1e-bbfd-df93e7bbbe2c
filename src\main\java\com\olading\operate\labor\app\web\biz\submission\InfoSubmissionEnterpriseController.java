package com.olading.operate.labor.app.web.biz.submission;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.domain.query.InfoSubmissionEnterpriseQuery;
import com.olading.operate.labor.domain.service.InfoSubmissionEnterpriseService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.file.FileInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionEnterpriseEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionEnterpriseVo;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.DefaultResourceLoader;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "企业信息报送相关接口")
@RestController
@RequestMapping("/api/supplier/infoenterprise")
@RequiredArgsConstructor
@Slf4j
public class InfoSubmissionEnterpriseController extends BusinessController {

    private final InfoSubmissionEnterpriseService infoSubmissionEnterpriseService;
    private final QueryService queryService;
    private final FileManager fileManager;

    private static final String INFO_SUBMISSION_ENTERPRISE_TEMPLATE = "/template/企业信息报送.xls";

    @Operation(summary = "企业信息报送记录列表")
    @PostMapping("/list")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public WebApiQueryResponse<InfoSubmissionEnterpriseVo> listInfoSubmissionEnterprise(@RequestBody QueryFilter<WebQueryInfoSubmissionEnterpriseFilters> request) {
        QueryFilter<InfoSubmissionEnterpriseQuery.Filters> filter = request.convert(WebQueryInfoSubmissionEnterpriseFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        //数据权限控制
        filter.getFilters().setCorporationIds(currentDataScope().get(OwnerType.CORPORATION));
        DataSet<InfoSubmissionEnterpriseVo> ds = queryService.queryInfoSubmissionEnterprise(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }



    @Operation(summary = "更新企业信息报送记录")
    @PostMapping("/update")
    public WebApiResponse<Void> updateInfoSubmissionEnterprise(@Valid @RequestBody InfoSubmissionEnterpriseParam param) {
        InfoSubmissionEnterpriseVo vo = new InfoSubmissionEnterpriseVo();
        BeanUtils.copyProperties(param, vo);

        infoSubmissionEnterpriseService.updateInfoSubmissionEnterprise(currentTenant(), vo);
        return WebApiResponse.success();
    }

    @Operation(summary = "企业信息报送记录详情")
    @PostMapping("/query")
    public WebApiResponse<InfoSubmissionEnterpriseVo> queryInfoSubmissionEnterprise(@Valid @RequestBody IdRequest request) {
        InfoSubmissionEnterpriseVo vo = infoSubmissionEnterpriseService.queryInfoSubmissionEnterprise(request.getId());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "删除企业信息报送记录")
    @PostMapping("/delete")
    public WebApiResponse<Void> deleteInfoSubmissionEnterprise(@Valid @RequestBody IdRequest request) {
        infoSubmissionEnterpriseService.deleteInfoSubmissionEnterprise(currentTenant(), request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "企业信息报送文件下载")
    @PostMapping(value = "/download")
    public void downloadInfoSubmissionEnterprise(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody SupplierCorporationIdRequest param) {
        try {
            // 根据作业主体ID查询企业信息报送记录列表
            List<InfoSubmissionEnterpriseEntity> entities = infoSubmissionEnterpriseService.queryInfoSubmissionEnterpriseBySupplierCorporationId(param.getSupplierCorporationId());

            // 读取模板文件
            Workbook workbook = new HSSFWorkbook(new DefaultResourceLoader().getResource(INFO_SUBMISSION_ENTERPRISE_TEMPLATE).getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 填充数据到模板
            fillTemplateData(sheet, entities);

            // 设置文件名
            String fileName = String.format("企业信息报送_%s.xls", param.getSupplierCorporationId());
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");

            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

            // 输出文件
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("下载企业信息报送文件成功，作业主体ID: {}, 记录数: {}", param.getSupplierCorporationId(), entities.size());

        } catch (Exception e) {
            log.error("下载企业信息报送文件失败", e);
            throw new RuntimeException("下载文件失败: " + e.getMessage());
        }
    }

    @Data
    public static class WebQueryInfoSubmissionEnterpriseFilters {
        @Schema(description = "企业信息报送记录ID")
        private Long id;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "报送状态")
        private String reportStatus;

        @Schema(description = "名称（姓名）")
        private String name;

        @Schema(description = "统一社会信用代码（纳税人识别号）")
        private String socialCreditCode;

        @Schema(description = "创建时间-起")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDateTime createTimeEnd;

        public InfoSubmissionEnterpriseQuery.Filters convert() {
            return Beans.copyBean(this, InfoSubmissionEnterpriseQuery.Filters.class);
        }
    }

    @Data
    public static class InfoSubmissionEnterpriseParam {
        @Schema(description = "企业信息报送记录ID")
        private Long id;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "报送状态")
        private String reportStatus;

        @Schema(description = "名称（姓名）")
        private String name;

        @Schema(description = "统一社会信用代码（纳税人识别号）")
        private String socialCreditCode;

        @Schema(description = "平台内的平台名称")
        private String platformName;

        @Schema(description = "平台内的平台唯一标识码")
        private String platformUniqueCode;

        @Schema(description = "经营开始时间")
        private String startDate;

        @Schema(description = "经营结束时间")
        private String endDate;

        @Schema(description = "信息状态标识")
        private String infoStatusFlag;
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "ID不能为空")
        @Schema(description = "记录ID")
        private Long id;
    }

    @Data
    public static class SupplierCorporationIdRequest {
        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;
    }

    /**
     * 填充模板数据
     */
    private void fillTemplateData(Sheet sheet, List<InfoSubmissionEnterpriseEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        // 从第三行开始写入数据（前两行是表头）
        int rowIndex = 2;
        int sequenceNumber = 1;

        for (InfoSubmissionEnterpriseEntity entity : entities) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // A列：序号
            setCellValue(row, 0, String.valueOf(sequenceNumber));

            // B列：报送状态
            setCellValue(row, 1, entity.getReportStatus());

            // C列：名称（姓名）
            setCellValue(row, 2, entity.getName());

            // D列：统一社会信用代码（纳税人识别号）
            setCellValue(row, 3, entity.getSocialCreditCode());

            // E列：平台内的平台名称
            setCellValue(row, 4, entity.getPlatformName());

            // F列：平台内的平台唯一标识码
            setCellValue(row, 5, entity.getPlatformUniqueCode());

            // G列：经营开始时间
            setCellValue(row, 6, entity.getStartDate());

            // H列：经营结束时间
            setCellValue(row, 7, entity.getEndDate());

            // I列：信息状态标识
            setCellValue(row, 8, entity.getInfoStatusFlag());

            rowIndex++;
            sequenceNumber++;
        }
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Row row, int columnIndex, String value) {
        if (row.getCell(columnIndex) == null) {
            row.createCell(columnIndex);
        }
        row.getCell(columnIndex).setCellValue(value != null ? value : "");
    }

    /**
     * 根据文件ID下载文件
     */
    private void downloadFileById(String fileId, String fileName, HttpServletResponse response) {
        try (OutputStream output = response.getOutputStream()) {
            FileInfo fileInfo = fileManager.getInfo(fileId);

            response.setContentType("application/octet-stream");
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

            fileManager.load(fileId, output);
        } catch (IOException e) {
            throw new IllegalStateException("下载文件失败: " + fileId, e);
        }
    }
}
