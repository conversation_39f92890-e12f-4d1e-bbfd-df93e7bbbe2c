package com.olading.operate.labor.domain.service;

import com.olading.operate.labor.app.web.biz.enums.EmpStatusEnum;
import com.olading.operate.labor.app.web.biz.labor.vo.ImportLaborRow;
import com.olading.operate.labor.app.web.biz.labor.vo.LaborBasicVo;
import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborInsertParam;
import com.olading.operate.labor.domain.share.user.UserManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierLaborService {

    private final LaborInfoRepository laborInfoRepository;

    private final CustomerService customerService;

    private final BusinessContractService businessContractService;

    private final CorporationManager corporationManager;

    private final UserManager userManager;

    private final InfoSubmissionLaborService infoSubmissionLaborService;

    public Long createSupplierLabor(SupplierLaborVo param, Long supplierId, TenantInfo tenantInfo) {
        SupplierLaborEntity laborEntity = laborInfoRepository.findLaborByIdCard(param.getIdCard(), supplierId);
        if (laborEntity == null) {
            laborEntity = param.toLaborEntity(supplierId, tenantInfo);
            laborInfoRepository.saveLabor(laborEntity);
        } else {
            if (!laborEntity.getCellphone().equals(param.getCellphone())) {
                throw new ApiException("证件号已存在但与当前手机号不一致", ApiException.SYSTEM_ERROR);
            }
        }
        LaborInfoEntity laborInfoEntity = laborInfoRepository.findLaborInfoByContract(supplierId, laborEntity.getId(), param.getContractId());
        if (laborInfoEntity != null) {
            throw new ApiException("人员已存在", ApiException.SYSTEM_ERROR);
        }
        laborInfoEntity = param.toLaborInfo(null);
        laborInfoEntity.setLaborId(laborEntity.getId());
        laborInfoRepository.saveLaborInfo(laborInfoEntity);
        return laborInfoEntity.getId();
    }

    public SupplierLaborVo getSupplierLaborDetail(Long id, Long supplierId) {
        return laborInfoRepository.findLaborInfoWithSupplierById(id, supplierId);
    }

    public void updateSupplierLabor(SupplierLaborVo param) {
        LaborInfoEntity labor = laborInfoRepository.findLaborById(param.getId());
        if (labor == null) {
            throw new ApiException("修改的人员信息不存在", ApiException.SYSTEM_ERROR);
        }
        LaborInfoEntity laborInfo = param.toLaborInfo(labor);
        laborInfo.setId(param.getId());
        laborInfoRepository.updateLaborInfo(laborInfo);
    }

    public void verifyImportLaborRow(Long supplierId, List<ImportLaborRow> models, Map<OwnerType, Set<Long>> ownerTypeSetMap) {

        //查询作业主体列表、客户、服务合同列表
        List<SupplierCorporationEntity> corporations = corporationManager.queryCorporationBySupplierId(supplierId);
        Map<String, SupplierCorporationEntity> corporationMap = corporations.stream().collect(Collectors.toMap(SupplierCorporationEntity::getName, v -> v, (k1, k2) -> k1));
        List<CustomerWithInfo> customers = customerService.queryCustomerBySupplier(supplierId);
        List<ContractVo> contracts = businessContractService.queryContractBySupplier(supplierId);
        Map<String, CustomerWithInfo> customerWithInfoMap = customers.stream().collect(Collectors.toMap(CustomerWithInfo::getName, v -> v, (k1, k2) -> k1));
        Map<String, ContractVo> contractVoMap = contracts.stream().collect(Collectors.toMap(ContractVo::getName, v -> v, (k1, k2) -> k1));
        Set<Long> contractIds = ownerTypeSetMap.get(OwnerType.CONTRACT);
        models.forEach(model -> {
            //服务合同校验
            if (StringUtils.isNotBlank(model.getContractName())) {
                ContractVo contractVo = contractVoMap.get(model.getContractName());
                if (contractVo == null) {
                    model.pushError(model.getContractName(), "服务合同不存在", "");
                } else {
                    if (!contractIds.contains(contractVo.getId())) {
                        model.pushError(model.getContractName(), "您没有当前服务合同的权限，请联系管理员添加", "");
                    }
                    model.setContractId(contractVo.getId());

                    if (StringUtils.isNotBlank(model.getCustomerName())) {
                        CustomerWithInfo customer = customerWithInfoMap.get(model.getCustomerName());
                        if (customer == null) {
                            model.pushError(model.getCustomerName(), "客户不存在", "");
                        } else {
                            if (!customer.getId().equals(contractVo.getCustomerId())) {
                                model.pushError(model.getCustomerName(), "服务合同不属于当前客户", "");
                            }
                            model.setCustomerId(customer.getId());
                        }
                    }
                    if (StringUtils.isNotBlank(model.getCorporationName())) {
                        SupplierCorporationEntity corporation = corporationMap.get(model.getCorporationName());
                        if (corporation == null) {
                            model.pushError(model.getCorporationName(), "作业主体不存在", "");
                        } else {
                            if (!contractVo.getSupplierCorporationId().equals(corporation.getId())) {
                                model.pushError(model.getCorporationName(), "作业主体与合同的作业主体不匹配", "");
                            }
                            model.setCorporationId(corporation.getId());
                        }
                    }
                }
            }
        });
        List<Long> laborContractIds = models.stream().filter(model -> model.getErrors().isEmpty()).map(ImportLaborRow::getContractId).toList();
        List<String> cards = models.stream().filter(model -> model.getErrors().isEmpty()).map(ImportLaborRow::getIdCard).toList();
        //人员校验
        //查询合同下的人员列表
        List<LaborBasicVo> laborInfos = laborInfoRepository.findLaborByContract(laborContractIds,supplierId);
        Map<Long, List<LaborBasicVo>> contractLabor = laborInfos.stream().collect(Collectors.groupingBy(LaborBasicVo::getContractId));
        List<SupplierLaborEntity> idCards = laborInfoRepository.findLaborsByIdCard(supplierId, cards);
        Map<String, SupplierLaborEntity> idCardMap = idCards.stream().collect(Collectors.toMap(SupplierLaborEntity::getIdCard, v -> v, (k1, k2) -> k1));
        models.stream().filter(model -> model.getErrors().isEmpty()).forEach(model -> {
            List<LaborBasicVo> infoVos = contractLabor.get(model.getContractId());
            if (CollectionUtils.isNotEmpty(infoVos)) {
                Optional<LaborBasicVo> first = infoVos.stream().filter(labor -> labor.getIdCard().equals(model.getIdCard())).findFirst();
                if (first.isPresent()) {
                    model.pushError(model.getIdCard(), "人员证件号码已存在", "");
                }
            }
            if (StringUtils.isNotBlank(model.getIdCard())) {
                SupplierLaborEntity labor = idCardMap.get(model.getIdCard());
                if (labor != null) {
                    if (StringUtils.isNotBlank(model.getName()) && !labor.getName().equals(model.getName())) {
                        model.pushError(model.getIdCard(), "人员姓名与证件号码不匹配", "");
                    }
                    if (StringUtils.isNotBlank(model.getCellPhone()) && !labor.getCellphone().equals(model.getCellPhone())) {
                        model.pushError(model.getIdCard(), "手机号与证件号码不匹配", "");
                    }
                }
            }
        });
        //第三次筛选，晒出重复人员
        Map<String, String> laborMap = new HashMap<>();
        Map<String, String> idCardsMap = new HashMap<>();
        models.stream().filter(model -> model.getErrors().isEmpty()).forEach(model -> {
            String laborStr = model.getContractId() + model.getIdCard();
            String value = model.getName() + model.getCellPhone();
            if (laborMap.containsKey(laborStr)) {
                model.pushError(model.getIdCard(), "人员在文件中已存在", "");
            } else {
                laborMap.put(laborStr, value);
            }
            if (idCardsMap.containsKey(model.getIdCard())) {
                if (idCardsMap.get(model.getIdCard()).equals(value)) {
                    model.pushError(model.getIdCard(), "同一证件号不能对应不同的姓名和手机号", "");
                }
            } else {
                idCardsMap.put(model.getIdCard(), value);
            }
        });
    }

    public void deleteSupplierLabor(Long id) {
        laborInfoRepository.deleteLaborInfoById(id);
    }

    public void addSupplierLabor(Long supplierId, List<ImportLaborRow> successDataList, TenantInfo tenant) {
        successDataList.forEach(model -> {
            SupplierLaborEntity entity = new SupplierLaborEntity(tenant, supplierId);
            entity.setName(model.getName());
            entity.setIdCard(model.getIdCard());
            entity.setCellphone(model.getCellPhone());
            entity.setSupplierId(supplierId);
            laborInfoRepository.saveLabor(entity);
            LaborInfoEntity laborInfoEntity = new LaborInfoEntity();
            laborInfoEntity.setLaborId(entity.getId());
            laborInfoEntity.setJoinDate(LocalDate.now());
            laborInfoEntity.setContractId(model.getContractId());
            laborInfoEntity.setSupplierCorporationId(model.getCorporationId());
            laborInfoEntity.setEmpStatus(EmpStatusEnum.ON_THE_JOB.name());
            laborInfoEntity.setBankCard(model.getBankCard());
            laborInfoEntity.setCardBank(model.getCardBank());
            laborInfoEntity.setSupplierId(model.getSupplierId());
            laborInfoRepository.saveLaborInfo(laborInfoEntity);
        });
    }

    public void userVerifySuccess(Long supplierId, Long userId, OcrIdentifyResult result) {
        userManager.identifyUser(userId, result.getIdNo(), result.getName());
        SupplierLaborEntity labor = laborInfoRepository.findLaborByIdCard(result.getIdNo(), supplierId);
        if (labor == null) {
            throw new ApiException("人员不存在", ApiException.API_PARAM_ERROR);
        }
        if (!labor.getName().equals(result.getName())) {
            throw new ApiException("身份信息不匹配，请联系服务人员处理", ApiException.API_PARAM_ERROR);
        }
        labor.setAuthStatus(true);
        labor.setAuthTime(LocalDateTime.now());
        labor.setBirthdayDate(result.getBirth());
        labor.setIdCardPeriod(result.getValidDate());
        laborInfoRepository.updateSupplierLaborInfo(labor);
    }

    public void updateLaborProtocol(CorporationProtocolEntity protocol) {
        List<LaborProtocolEntity> laborProtocol = laborInfoRepository.findLaborProtocolByCardAndCorporationId(List.of(protocol.getIdCard()), protocol.getSupplierCorporationId());
        LaborProtocolEntity entity = null;
        if (CollectionUtils.isNotEmpty(laborProtocol)) {
            entity = laborProtocol.get(0);
            entity.setProtocolStatus(protocol.getSignStatus().name());
            laborInfoRepository.updateLaborProtocol(entity);
        } else {
            SupplierLaborEntity labor = laborInfoRepository.findLaborByIdCard(protocol.getIdCard(), protocol.getSupplierId());
            entity = new LaborProtocolEntity();
            entity.setName(labor.getName());
            entity.setIdCard(protocol.getIdCard());
            entity.setSupplierId(protocol.getSupplierId());
            entity.setSupplierCorporationId(protocol.getSupplierCorporationId());
            entity.setProtocolStatus(protocol.getSignStatus().name());
            entity.setStartDate(protocol.getStartDate());
            entity.setEndDate(protocol.getEndDate());
            laborInfoRepository.saveLaborProtocol(entity);
        }
        try {
            InfoSubmissionLaborInsertParam param = new InfoSubmissionLaborInsertParam(protocol.getSupplierId(), protocol.getSupplierCorporationId(), protocol.getIdCard());
            infoSubmissionLaborService.insertInfoSubmissionLabor(param);
        } catch (Exception e) {
            log.error("同步人员信息报错", e);
        }

    }
}
