package com.olading.operate.labor.app.web.biz.tax;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.query.ValueAddedTaxDeclareQuery;
import com.olading.operate.labor.domain.service.ValueAddedTaxDeclareService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.tax.ValueAddedTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDetailVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Tag(name = "增值税申报相关接口")
@RestController
@RequestMapping("/api/supplier/valueaddedtax")
@RequiredArgsConstructor
@Slf4j
public class ValueAddedTaxDeclareController extends BusinessController {

    private final ValueAddedTaxDeclareService valueAddedTaxDeclareService;
    private final QueryService queryService;

    private static final String VALUE_ADDED_TAX_TEMPLATE = "/template/增值税申报表.xls";

    @Operation(summary = "增值税申报记录列表")
    @PostMapping("/list")
    public WebApiQueryResponse<ValueAddedTaxDeclareVo> listValueAddedTaxDeclare(@RequestBody QueryFilter<WebQueryValueAddedTaxDeclareFilters> request) {
        QueryFilter<ValueAddedTaxDeclareQuery.Filters> filter = request.convert(WebQueryValueAddedTaxDeclareFilters::convert);
        filter.sort("id", Direction.DESCENDING);

        DataSet<ValueAddedTaxDeclareVo> ds = queryService.queryValueAddedTaxDeclare(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "新增增值税申报记录")
    @PostMapping("/add")
    public WebApiResponse<Long> addValueAddedTaxDeclare(@Valid @RequestBody ValueAddedTaxDeclareParam param) {
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        BeanUtils.copyProperties(param, vo);
        vo.setSupplierId(currentSupplierId());

        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(currentTenant(), vo);
        return WebApiResponse.success(entity.getId());
    }

    @Operation(summary = "更新增值税申报记录")
    @PostMapping("/update")
    public WebApiResponse<Void> updateValueAddedTaxDeclare(@Valid @RequestBody ValueAddedTaxDeclareParam param) {
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        BeanUtils.copyProperties(param, vo);

        valueAddedTaxDeclareService.updateValueAddedTaxDeclare(currentTenant(), vo);
        return WebApiResponse.success();
    }

    @Operation(summary = "增值税申报记录详情")
    @PostMapping("/query")
    public WebApiResponse<ValueAddedTaxDeclareVo> queryValueAddedTaxDeclare(@Valid @RequestBody IdRequest request) {
        ValueAddedTaxDeclareVo vo = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(request.getId());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "删除增值税申报记录")
    @PostMapping("/delete")
    public WebApiResponse<Void> deleteValueAddedTaxDeclare(@Valid @RequestBody IdRequest request) {
        valueAddedTaxDeclareService.deleteValueAddedTaxDeclare(currentTenant(), request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "更新增值税申报状态为已申报")
    @PostMapping("/updateTaxStatus")
    public WebApiResponse<Void> updateTaxStatusToDeclared(@Valid @RequestBody IdRequest request) {
        valueAddedTaxDeclareService.updateTaxStatusToDeclared(request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "增值税申报下载")
    @PostMapping(value = "/download/declarationRecord")
    public void downloadDeclarationRecord(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody IdRequest param) {
        try {
            // 获取增值税申报记录详情
            ValueAddedTaxDeclareVo vo = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(param.getId());

            // 读取模板文件
            Workbook workbook = new HSSFWorkbook(new DefaultResourceLoader().getResource(VALUE_ADDED_TAX_TEMPLATE).getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 填充数据到模板
            fillTemplateData(sheet, vo);

            // 设置文件名
            String fileName = String.format("%s_增值税申报表.xls", vo.getIncomeTaxMonth() != null ? vo.getIncomeTaxMonth() : "");

            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("增值税申报表模板下载失败", e);
            throw new RuntimeException("增值税申报表模板下载失败: " + e.getMessage());
        }
    }

    /**
     * 填充模板数据
     */
    /**
     * 填充模板数据（严格按模板列顺序）
     */
    private void fillTemplateData(Sheet sheet, ValueAddedTaxDeclareVo vo) {
        if (vo == null || CollectionUtils.isEmpty(vo.getDetails())) {
            return;
        }

        List<ValueAddedTaxDetailVo> details = vo.getDetails();

        // 从第三行开始写入数据（前两行是表头）
        int rowIndex = 2;
        int sequenceNumber = 1;

        for (ValueAddedTaxDetailVo detail : details) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // 0 序号
            createCellWithValue(row, 0, String.valueOf(sequenceNumber));

            // 1 姓名
            createCellWithValue(row, 1, detail.getName());

            // 2 证件类型
            createCellWithValue(row, 2, "居民身份证");

            // 3 证件号码
            createCellWithValue(row, 3, detail.getIdCard());

            // 4 国家或地区
            createCellWithValue(row, 4, detail.getCountryRegion());

            // 5 地址
            createCellWithValue(row, 5, detail.getAddress());

            // 6 用户名称
            createCellWithValue(row, 6, detail.getUserName());

            // 7 用户唯一标识码（模板缺失，留空）
            createCellWithValue(row, 7, detail.getUserUniqueCode());

            // 8 联系电话（模板缺失，留空）
            createCellWithValue(row, 8, detail.getCellPhone());

            // 9 增值税计税依据
            createCellWithValue(row, 9, detail.getTaxBasis() != null ? detail.getTaxBasis().toString() : "");

            // 10 征收品目
            createCellWithValue(row, 10, detail.getTaxItem());

            // 11 征收率
            createCellWithValue(row, 11, detail.getTaxRate());

            // 12 本期应纳税额（增值税）
            createCellWithValue(row, 12, detail.getVatAmount() != null ? detail.getVatAmount().toString() : "");

            // 13 增值税减免性质代码
            createCellWithValue(row, 13, detail.getVatExemptionCode());

            // 14 增值税减免税额
            createCellWithValue(row, 14, detail.getVatExemptionAmount() != null ? detail.getVatExemptionAmount().toString() : "");

            // 15 增值税应补(退)税额
            createCellWithValue(row, 15, detail.getVatPayable() != null ? detail.getVatPayable().toString() : "");

            // 16 城市维护建设税适用税率
            createCellWithValue(row, 16, detail.getUrbanTaxRate());

            // 17 城市维护建设税本期应纳税额
            createCellWithValue(row, 17, detail.getUrbanTaxAmount() != null ? detail.getUrbanTaxAmount().toString() : "");

            // 18 城市维护建设税减免性质代码
            createCellWithValue(row, 18, detail.getUrbanExemptionCode());

            // 19 城市维护建设税减免税额
            createCellWithValue(row, 19, detail.getUrbanExemptionAmount() != null ? detail.getUrbanExemptionAmount().toString() : "");

            // 20 城市维护建设税应补(退)税额
            createCellWithValue(row, 20, detail.getUrbanTaxPayable() != null ? detail.getUrbanTaxPayable().toString() : "");

            // 21 教育费附加适用税率
            createCellWithValue(row, 21, detail.getEduTaxRate());

            // 22 教育费附加本期应纳税额
            createCellWithValue(row, 22, detail.getEduTaxAmount() != null ? detail.getEduTaxAmount().toString() : "");

            // 23 教育费附加减免性质代码
            createCellWithValue(row, 23, detail.getEduExemptionCode());

            // 24 教育费附加减免税额
            createCellWithValue(row, 24, detail.getEduExemptionAmount() != null ? detail.getEduExemptionAmount().toString() : "");

            // 25 教育费附加应补(退)税额
            createCellWithValue(row, 25, detail.getEduTaxPayable() != null ? detail.getEduTaxPayable().toString() : "");

            // 26 地方教育附加适用税率
            createCellWithValue(row, 26, detail.getLocalEduTaxRate());

            // 27 地方教育附加本期应纳税额
            createCellWithValue(row, 27, detail.getLocalEduTaxAmount() != null ? detail.getLocalEduTaxAmount().toString() : "");

            // 28 地方教育附加减免性质代码
            createCellWithValue(row, 28, detail.getLocalEduExemptionCode());

            // 29 地方教育附加减免税额
            createCellWithValue(row, 29, detail.getLocalEduExemptionAmount() != null ? detail.getLocalEduExemptionAmount().toString() : "");

            // 30 地方教育附加应补(退)税额
            createCellWithValue(row, 30, detail.getLocalEduTaxPayable() != null ? detail.getLocalEduTaxPayable().toString() : "");

            rowIndex++;
            sequenceNumber++;
        }
    }

    /**
     * 创建单元格并设置值
     */
    private void createCellWithValue(Row row, int columnIndex, String value) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        cell.setCellValue(value != null ? value : "");
    }


    @Data
    public static class IdRequest {
        @NotNull(message = "增值税申报记录ID不能为空")
        @Schema(description = "增值税申报记录ID")
        private Long id;
    }


    @Data
    public static class WebQueryValueAddedTaxDeclareFilters {
        @Schema(description = "增值税申报记录ID")
        private Long id;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        private String taxPaymentPeriod;

        @Schema(description = "个税申报月")
        private String incomeTaxMonth;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        @Schema(description = "生成状态")
        private String status;

        @Schema(description = "申报状态")
        private String taxStatus;

        @Schema(description = "创建时间-起")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDateTime createTimeEnd;

        public ValueAddedTaxDeclareQuery.Filters convert() {
            return Beans.copyBean(this, ValueAddedTaxDeclareQuery.Filters.class);
        }
    }

    @Data
    public static class ValueAddedTaxDeclareParam {
        @Schema(description = "增值税申报记录ID")
        private Long id;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        private String taxPaymentPeriod;

        @Schema(description = "个税申报月")
        private String incomeTaxMonth;

        @Schema(description = "纳税人数")
        private String taxpayersCount;

        @Schema(description = "本期收入")
        private String currentIncome;
    }
}
