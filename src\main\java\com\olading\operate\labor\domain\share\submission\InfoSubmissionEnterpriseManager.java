package com.olading.operate.labor.domain.share.submission;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.util.ThreadPoolUtil;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionEnterpriseVo;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;

@Transactional
@Component
@RequiredArgsConstructor
@Slf4j
public class InfoSubmissionEnterpriseManager {

    private final EntityManager em;
    private final FileManager fileManager;
    private final PlatformTransactionManager transactionManager;

    /**
     * 新增企业信息报送记录
     */
    public InfoSubmissionEnterpriseEntity addInfoSubmissionEnterprise(TenantInfo tenantInfo, InfoSubmissionEnterpriseVo vo) {
        // 创建实体
        InfoSubmissionEnterpriseEntity entity = new InfoSubmissionEnterpriseEntity(tenantInfo);
        copyToEntity(vo, entity);
        entity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATING.name());
        entity = em.merge(entity);
        em.flush();

        // 异步生成文件
        final Long entityId = entity.getId();
        ThreadPoolUtil.executeCommonThreadPool(() -> {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = transactionManager.getTransaction(def);

            try {
                // 生成企业信息报送文件
                String fileContent = generateEnterpriseInfoFile(entityId);
                
                // 保存文件
                String fileId = fileManager.save(
                    "企业信息报送_" + entityId + ".txt",
                    new ByteArrayInputStream(fileContent.getBytes()),
                    LocalDateTime.now().plusYears(1),
                    OwnerType.SUPPLIER,
                    String.valueOf(vo.getSupplierId())
                );
                
                // 更新记录状态和文件ID
                InfoSubmissionEnterpriseEntity updateEntity = em.find(InfoSubmissionEnterpriseEntity.class, entityId);
                if (updateEntity != null) {
                    updateEntity.setFileId(fileId);
                    updateEntity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATED.name());
                    em.merge(updateEntity);
                }

                transactionManager.commit(status);
                log.info("企业信息报送文件生成成功，记录ID: {}, 文件ID: {}", entityId, fileId);
                
            } catch (Exception e) {
                transactionManager.rollback(status);
                log.error("异步生成企业信息报送文件失败，记录ID: {}，状态保持为生成中", entityId, e);
            }
        });

        return entity;
    }

    /**
     * 更新企业信息报送记录
     */
    public InfoSubmissionEnterpriseEntity updateInfoSubmissionEnterprise(InfoSubmissionEnterpriseVo vo) {
        InfoSubmissionEnterpriseEntity entity = getInfoSubmissionEnterpriseById(vo.getId());
        copyToEntity(vo, entity);
        return em.merge(entity);
    }

    /**
     * 查询企业信息报送记录详情
     */
    public InfoSubmissionEnterpriseVo queryInfoSubmissionEnterprise(Long id) {
        InfoSubmissionEnterpriseEntity entity = getInfoSubmissionEnterpriseById(id);
        InfoSubmissionEnterpriseVo vo = new InfoSubmissionEnterpriseVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 删除企业信息报送记录
     */
    public void deleteInfoSubmissionEnterprise(Long id) {
        InfoSubmissionEnterpriseEntity entity = getInfoSubmissionEnterpriseById(id);
        em.remove(entity);
    }

    /**
     * 根据ID获取企业信息报送记录
     */
    public InfoSubmissionEnterpriseEntity getInfoSubmissionEnterpriseById(Long id) {
        InfoSubmissionEnterpriseEntity entity = em.find(InfoSubmissionEnterpriseEntity.class, id);
        if (entity == null) {
            throw new BusinessException("企业信息报送记录不存在");
        }
        return entity;
    }

    /**
     * 生成企业信息报送文件内容
     * TODO: 这里需要根据实际业务需求实现具体的文件生成逻辑
     */
    private String generateEnterpriseInfoFile(Long entityId) {
        // 模拟文件生成过程
        try {
            Thread.sleep(2000); // 模拟文件生成耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return "企业信息报送文件内容 - 记录ID: " + entityId + "\n生成时间: " + LocalDateTime.now();
    }

    /**
     * 复制VO到Entity
     */
    private void copyToEntity(InfoSubmissionEnterpriseVo vo, InfoSubmissionEnterpriseEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getStatus() != null) {
            entity.setStatus(vo.getStatus());
        }
        if (vo.getFileId() != null) {
            entity.setFileId(vo.getFileId());
        }
    }
}
