package com.olading.operate.labor.domain.salary;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.SurtaxCodeEnum;
import com.olading.operate.labor.domain.corporation.SurtaxData;
import com.olading.operate.labor.domain.salary.engine.PersonalIncomeTaxCalculationEngine;
import com.olading.operate.labor.domain.salary.engine.PreviousIncomeDeductionManager;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationRequest;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationResult;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareManager;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDetailManager;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * 增值税和附加税计算测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class VatSurtaxCalculationTest extends BaseTest {

    @Autowired
    private SalaryManager salaryManager;

    @Autowired
    private PersonalIncomeTaxDetailManager taxDetailManager;

    @Autowired
    private PersonalIncomeTaxDeclareManager taxDeclareManager;

    @Autowired
    private PreviousIncomeDeductionManager previousIncomeManager;

    @Autowired
    private CorporationManager corporationManager;

    @Autowired
    private PersonalIncomeTaxCalculationEngine taxEngine;

    private CorporationConfigEntity corporationConfig;

    public void beforeEach() {
        // 设置作业主体配置
        corporationConfig = corporationManager.queryCorporationConfig(1L);
    }



    @Test
    void testVatCalculation_BelowThreshold() {

       /* this.withTransaction(s -> {
            // 设置作业主体配置
            corporationConfig = corporationManager.queryCorporationConfig(1L);

            // 设置附加税配置
            List<SurtaxData> surtaxDataList = new ArrayList<>();
            surtaxDataList.add(SurtaxData.builder()
                    .surtaxCode(SurtaxCodeEnum.URBAN_MAINTENANCE_TAX)
                    .name("城市维护建设税")
                    .rate(new BigDecimal("0.07"))  // 7%
                    .discount_rate(BigDecimal.ZERO)
                    .build());
            surtaxDataList.add(SurtaxData.builder()
                    .surtaxCode(SurtaxCodeEnum.EDUCATION_SURCHARGE)
                    .name("教育费附加")
                    .rate(new BigDecimal("0.03"))  // 3%
                    .discount_rate(BigDecimal.ZERO)
                    .build());
            surtaxDataList.add(SurtaxData.builder()
                    .surtaxCode(SurtaxCodeEnum.LOCAL_EDUCATION_SURCHARGE)
                    .name("地方教育附加")
                    .rate(new BigDecimal("0.02"))  // 2%
                    .discount_rate(BigDecimal.ZERO)
                    .build());

            corporationConfig.setSurtaxData(JSONUtil.toJsonStr(surtaxDataList));
            em.merge(corporationConfig);
            return null;
        });*/


        // 测试当月应发金额累计低于起征点的情况
        final BigDecimal bigDecimal = salaryManager.queryMonthlyTotalPayableAmount(1L, "2025-02");
        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2025-02");
        request.setPayableAmount(new BigDecimal("50000")); // 5万，加上已确认的0，总计5万，低于10万起征点
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证增值税为0
        assertEquals(BigDecimal.ZERO, result.getVatAmount());
        assertEquals(BigDecimal.ZERO, result.getAdditionalTaxAmount());
        assertEquals(BigDecimal.ZERO, result.getUrbanConstructionTax());
        assertEquals(BigDecimal.ZERO, result.getEducationSurcharge());
        assertEquals(BigDecimal.ZERO, result.getLocalEducationSurcharge());
    }

    @Test
    void testVatCalculation_AboveThreshold() {
        // 测试当月应发金额累计超过起征点的情况
        // Mock salaryManager返回当月已确认的应发金额累计为0（第一笔）
        final BigDecimal bigDecimal = salaryManager.queryMonthlyTotalPayableAmount(1L, "2024-01");

        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2024-01");
        request.setPayableAmount(new BigDecimal("150000")); // 15万，加上已确认的0，总计15万，超过10万起征点
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证增值税计算：(150000 - 100000) * 1% = 500
        assertEquals(new BigDecimal("500.00"), result.getVatAmount().setScale(2));

        // 验证附加税计算
        // 城市维护建设税：500 * 7% = 35
        assertEquals(new BigDecimal("35.00"), result.getUrbanConstructionTax().setScale(2));
        // 教育费附加：500 * 3% = 15
        assertEquals(new BigDecimal("15.00"), result.getEducationSurcharge().setScale(2));
        // 地方教育附加：500 * 2% = 10
        assertEquals(new BigDecimal("10.00"), result.getLocalEducationSurcharge().setScale(2));

        // 附加税总额：35 + 15 + 10 = 60
        assertEquals(new BigDecimal("60.00"), result.getAdditionalTaxAmount().setScale(2));
    }

    @Test
    void testVatCalculation_ExactThreshold() {
        // 测试当月应发金额累计正好等于起征点的情况
        // Mock salaryManager返回当月已确认的应发金额累计为0（第一笔）
        when(salaryManager.queryMonthlyTotalPayableAmount(1L, "2024-01")).thenReturn(BigDecimal.ZERO);

        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2024-01");
        request.setPayableAmount(new BigDecimal("100000")); // 10万，加上已确认的0，总计正好10万
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证增值税为0（等于起征点不征收）
        assertEquals(BigDecimal.ZERO, result.getVatAmount());
        assertEquals(BigDecimal.ZERO, result.getAdditionalTaxAmount());
    }

    @Test
    void testSurtaxCalculation_WithDiscount() {
        // 测试有优惠比例的附加税计算
        // Mock salaryManager返回当月已确认的应发金额累计为0（第一笔）
        when(salaryManager.queryMonthlyTotalPayableAmount(1L, "2024-01")).thenReturn(BigDecimal.ZERO);

        List<SurtaxData> surtaxDataList = new ArrayList<>();
        surtaxDataList.add(SurtaxData.builder()
                .surtaxCode(SurtaxCodeEnum.URBAN_MAINTENANCE_TAX)
                .name("城市维护建设税")
                .rate(new BigDecimal("0.07"))  // 7%
                .discount_rate(new BigDecimal("0.5"))  // 50%优惠
                .build());
        surtaxDataList.add(SurtaxData.builder()
                .surtaxCode(SurtaxCodeEnum.EDUCATION_SURCHARGE)
                .name("教育费附加")
                .rate(new BigDecimal("0.03"))  // 3%
                .discount_rate(new BigDecimal("0.5"))  // 50%优惠
                .build());
        surtaxDataList.add(SurtaxData.builder()
                .surtaxCode(SurtaxCodeEnum.LOCAL_EDUCATION_SURCHARGE)
                .name("地方教育附加")
                .rate(new BigDecimal("0.02"))  // 2%
                .discount_rate(new BigDecimal("0.5"))  // 50%优惠
                .build());

        corporationConfig.setSurtaxData(JSONUtil.toJsonStr(surtaxDataList));

        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2024-01");
        request.setPayableAmount(new BigDecimal("200000")); // 20万，加上已确认的0，总计20万
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证增值税计算：(200000 - 100000) * 1% = 1000
        assertEquals(new BigDecimal("1000.00"), result.getVatAmount().setScale(2));

        // 验证附加税计算（考虑50%优惠）
        // 城市维护建设税：1000 * 7% * (1-50%) = 35
        assertEquals(new BigDecimal("35.00"), result.getUrbanConstructionTax().setScale(2));
        // 教育费附加：1000 * 3% * (1-50%) = 15
        assertEquals(new BigDecimal("15.00"), result.getEducationSurcharge().setScale(2));
        // 地方教育附加：1000 * 2% * (1-50%) = 10
        assertEquals(new BigDecimal("10.00"), result.getLocalEducationSurcharge().setScale(2));

        // 附加税总额：35 + 15 + 10 = 60
        assertEquals(new BigDecimal("60.00"), result.getAdditionalTaxAmount().setScale(2));
    }

    @Test
    void testVatCalculation_WithExistingConfirmedSalaries() {
        // 测试当月已有其他已确认薪酬明细时的增值税增量计算
        // Mock salaryManager返回当月已确认的应发金额累计为80,000（已有其他薪酬明细）
        when(salaryManager.queryMonthlyTotalPayableAmount(1L, "2024-01")).thenReturn(new BigDecimal("80000"));

        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2024-01");
        request.setPayableAmount(new BigDecimal("30000")); // 3万，加上已确认的8万，总计11万，超过10万起征点
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证增值税增量计算（按新逻辑）：
        // 已确认累计80000未超过起征点，加上本期30000后总计110000超过起征点
        // 增值税 = (80000 + 30000 - 100000) × 1% = 10000 × 1% = 100
        assertEquals(new BigDecimal("100.00"), result.getVatAmount().setScale(2));

        // 验证附加税计算（基于增量增值税100）
        // 城市维护建设税：100 * 7% = 7
        assertEquals(new BigDecimal("7.00"), result.getUrbanConstructionTax().setScale(2));
        // 教育费附加：100 * 3% = 3
        assertEquals(new BigDecimal("3.00"), result.getEducationSurcharge().setScale(2));
        // 地方教育附加：100 * 2% = 2
        assertEquals(new BigDecimal("2.00"), result.getLocalEducationSurcharge().setScale(2));

        // 附加税总额：7 + 3 + 2 = 12
        assertEquals(new BigDecimal("12.00"), result.getAdditionalTaxAmount().setScale(2));
    }

    @Test
    void testVatCalculation_BothAboveThreshold() {
        // 测试当月已确认金额和新增金额都超过起征点时的增量计算
        // Mock salaryManager返回当月已确认的应发金额累计为120,000（已超过起征点）
        when(salaryManager.queryMonthlyTotalPayableAmount(1L, "2024-01")).thenReturn(new BigDecimal("120000"));

        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2024-01");
        request.setPayableAmount(new BigDecimal("50000")); // 5万，加上已确认的12万，总计17万
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证增值税增量计算（按新逻辑）：
        // 已确认累计120000已超过起征点，直接用本期应发金额计算
        // 增值税 = 50000 × 1% = 500
        assertEquals(new BigDecimal("500.00"), result.getVatAmount().setScale(2));

        // 验证附加税计算（基于增量增值税500）
        // 城市维护建设税：500 * 7% = 35
        assertEquals(new BigDecimal("35.00"), result.getUrbanConstructionTax().setScale(2));
        // 教育费附加：500 * 3% = 15
        assertEquals(new BigDecimal("15.00"), result.getEducationSurcharge().setScale(2));
        // 地方教育附加：500 * 2% = 10
        assertEquals(new BigDecimal("10.00"), result.getLocalEducationSurcharge().setScale(2));

        // 附加税总额：35 + 15 + 10 = 60
        assertEquals(new BigDecimal("60.00"), result.getAdditionalTaxAmount().setScale(2));
    }

    @Test
    void testNetPaymentCalculation_WithVatAndSurtax() {
        // 测试实发金额计算（扣除个税、增值税和附加税）
        // Mock salaryManager返回当月已确认的应发金额累计为0（第一笔）
        when(salaryManager.queryMonthlyTotalPayableAmount(1L, "2024-01")).thenReturn(BigDecimal.ZERO);

        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard("110101199001011234");
        request.setSupplierCorporationId(1L);
        request.setTaxPeriod("2024-01");
        request.setPayableAmount(new BigDecimal("150000")); // 15万
        request.setTaxFreeIncome(BigDecimal.ZERO);
        request.setOtherDeductions(BigDecimal.ZERO);
        request.setTaxReliefAmount(BigDecimal.ZERO);

        TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

        // 验证各项税费
        assertTrue(result.getVatAmount().compareTo(BigDecimal.ZERO) > 0);
        assertTrue(result.getAdditionalTaxAmount().compareTo(BigDecimal.ZERO) > 0);

        // 验证实发金额 = 应发金额 - 个税 - 增值税 - 附加税
        BigDecimal expectedNetPayment = request.getPayableAmount()
                .subtract(result.getCurrentWithholdingTax())
                .subtract(result.getVatAmount())
                .subtract(result.getAdditionalTaxAmount());

        assertEquals(expectedNetPayment.setScale(2), result.getNetPayment().setScale(2));
    }
}