package com.olading.operate.labor.domain.share.file;

import com.olading.boot.core.spi.FileStorageProvider;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.Utils;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;

@Transactional
@Component
@RequiredArgsConstructor
public class FileManager {

    private final EntityManager em;
    private final FileStorageProvider storageProvider;

    public String saveTemp(String name, InputStream input, OwnerType ownerType, String ownerId) {
        return save(name, input, LocalDateTime.now().plusDays(1), ownerType, ownerId);
    }

    /**
     * 保存一个文件
     */
    @SneakyThrows
    public String save(String name, InputStream input, LocalDateTime expiryTime, OwnerType ownerType, String ownerId) {

        String id = Utils.uuid();
        String storageId = storageProvider.writeFile(id, name, input);
        FileEntity file = new FileEntity(id, name, storageId, ownerType, ownerId);
        file.setExpiryTime(expiryTime);
        return em.merge(file).getId();
    }

    /**
     * 读取文件信息
     */
    public FileInfo getInfo(String id) {
        FileEntity file = require(id);

        FileInfo info = new FileInfo();
        info.setId(file.getId());
        info.setName(file.getName());
        return info;
    }

    /**
     * 读取文件内容
     */
    @SneakyThrows
    public void load(String id, OutputStream output) {
        FileEntity file = require(id);
        storageProvider.readFile(file.getStorageId(), output);
    }

    public byte[] load(String id) {
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        load(id, buff);
        return buff.toByteArray();
    }

    public InputStream loadAsStream(String id) {
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        load(id, buff);
        return new ByteArrayInputStream(buff.toByteArray());
    }

    @SneakyThrows
    public void delete(String id) {
        FileEntity file = require(id);
        storageProvider.deleteFile(file.getStorageId());
    }

    public void modify(String id, LocalDateTime expiryTime) {
        FileEntity file = require(id);
        if (expiryTime != null) {
            file.setExpiryTime(expiryTime);
        }
        em.merge(file);
    }

    /**
     * 标记文件为永久文件
     */
    public void markPermanent(String id) {
        modify(id, LocalDateTime.now().plusYears(1000));
    }

    public FileEntity require(String id) {
        FileEntity file = em.find(FileEntity.class, id);
        if (file == null) {
            throw new IllegalStateException("文件不存在");
        }
        return file;
    }

}
