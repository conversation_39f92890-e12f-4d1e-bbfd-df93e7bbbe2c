package com.olading.operate.labor.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignState;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.app.web.biz.enums.ProtocolTempEnum;
import com.olading.operate.labor.app.web.biz.protocol.ProtocolController;
import com.olading.operate.labor.app.web.biz.protocol.TemplateController;
import com.olading.operate.labor.app.web.biz.protocol.vo.ProtocolCreateDto;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.corporation.CorporationData;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.*;
import com.olading.operate.labor.domain.share.signing.CloudSigningService;
import com.olading.operate.labor.domain.share.signing.SignManager;
import com.olading.operate.labor.domain.share.signing.common.FieldValue;
import com.olading.operate.labor.domain.share.signing.enums.*;
import com.olading.operate.labor.domain.share.signing.request.*;
import com.olading.operate.labor.domain.share.signing.response.*;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.util.JSONUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity.create;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProtocolService {

    private final LaborInfoRepository laborInfoRepository;

    private final ProtocolManager protocolManager;

    private final ProtocolTemplateRepository protocolTemplateRepository;

    private final ProtocolRepository protocolRepository;

    private final SupplierService supplierService;

    private final CloudSigningService cloudSigningService;

    private final SignManager signManager;

    private final UserManager userManager;

    public List<ProtocolController.LaborProtocolVo> getLaborContract(TemplateController.SupplierAndUser supplierAndUser, ProtocolController.QueryLaborVo queryLaborVo) {
        return protocolRepository.queryLaborProtocol(queryLaborVo.getIdCard(), queryLaborVo.getCorporationId(), supplierAndUser.getSupplier().getId());

    }

    /**
     * 创建合同
     */
    @Transactional
    public void createProtocol(TemplateController.SupplierAndUser supplierAndUser, ProtocolCreateDto vo) {

        List<LaborInfoEntity> laborInfos = laborInfoRepository.findLaborInfoByIds(vo.getLaborInfoIdList());
        if (laborInfos.isEmpty()) {
            throw new ApiException("选择的劳务人员不存在", ApiException.API_PARAM_ERROR);
        }
        //查询作业主体信息
        CorporationData corporation = supplierService.getCorporation(supplierAndUser.getTenant(), vo.getCorporationId());
        List<Long> laborIds = laborInfos.stream().map(LaborInfoEntity::getId).distinct().toList();
        //查询劳务人员协议列表
        List<LaborInfoEntity> laborInfoList = laborInfoRepository.findLaborInfoByIds(laborIds);
        List<Long> ids = laborInfoList.stream().map(LaborInfoEntity::getLaborId).toList();
        List<SupplierLaborEntity> laborList = laborInfoRepository.findLaborByIds(ids);
        List<String> idCards = laborList.stream().map(SupplierLaborEntity::getIdCard).toList();
        //根据证件号查询劳务人员协议
        List<LaborProtocolEntity> laborProtocols = laborInfoRepository.findLaborProtocolByCardAndCorporationId(idCards, vo.getCorporationId());
        Set<String> signedSet = laborProtocols.stream().map(LaborProtocolEntity::getIdCard).collect(Collectors.toSet());
        List<SupplierLaborEntity> laborWaitSign = laborList.stream().filter(o -> !signedSet.contains(o.getIdCard())).toList();
        if (laborWaitSign.isEmpty()) {
            return;
        }
        //1.生成劳务合同记录
        laborWaitSign.stream().map(labor -> create(labor, supplierAndUser.getSupplier().getId(), vo.getCorporationId(),
                        vo.getProtocolName(), vo.getStartDate(), vo.getEndDate()))
                .forEach(laborInfoRepository::saveLaborProtocol);

        //2.查询模板
        CorporationProtocolTemplateEntity template = protocolTemplateRepository.queryProtocolTempById(vo.getTemplateId());
        if (template == null || !template.getStatus().equals(ProtocolTempEnum.ENABLED.name())) {
            throw new ApiException("模板不存在或未发布", ApiException.API_PARAM_ERROR);
        }
        //3.查询步骤
        List<CorporationProtocolTempStepEntity> steps = protocolTemplateRepository.queryTempStepByTempId(vo.getTemplateId());
        List<Long> stepIds = steps.stream().map(CorporationProtocolTempStepEntity::getId).toList();
        List<CorporationProtocolTempFiledEntity> templateFields = protocolTemplateRepository.queryTempFiledBySteps(stepIds);
        //4.保存合同
        laborWaitSign.forEach(labor -> {
            CorporationProtocolEntity protocol = new CorporationProtocolEntity(supplierAndUser.getTenant());
            protocol.setTemplateId(vo.getTemplateId());
            protocol.setSupplierId(supplierAndUser.getSupplier().getId());
            protocol.setSupplierCorporationId(vo.getCorporationId());
            protocol.setProtocolName(vo.getProtocolName() + "_" + labor.getName());
            protocol.setCustomerId(vo.getCustomerId());
            protocol.setContractId(vo.getContractId());
            protocol.setStartDate(vo.getStartDate());
            protocol.setEndDate(vo.getEndDate());
            protocol.setSilenceSign(true);
            protocol.setSignStatus(EnumContractSignStatus.DRAFT);
            protocol.setCreateDate(vo.getStartDate());
            protocol.setAgentTemplate(template.getAgentTemplate());
            protocol.setIdCard(labor.getIdCard());
            protocolRepository.saveProtocol(protocol);
            //5.保存步骤
            steps.forEach(step -> {
                CorporationProtocolStepEntity protocolStep = new CorporationProtocolStepEntity(supplierAndUser.getTenant());
                protocolStep.setProtocolId(protocol.getId());
                protocolStep.setStepName(step.getStepName());
                protocolStep.setOperate(step.getOperate());
                protocolStep.setSortby(step.getSortBy());
                protocolStep.setCurrentStep(false);
                protocolStep.setReceived(false);
                protocolStep.setSupplierId(supplierAndUser.getSupplier().getId());
                protocolStep.setSupplierCorporationId(vo.getCorporationId());
                if (step.getOperate().equals(EnumOperateType.SIGN)) {
                    protocolStep.setIdCard(labor.getIdCard());
                }
                protocolRepository.saveProtocolSteps(protocolStep);
                protocolStep.setFlowNo("flow" + protocolStep.getId() + format(LocalDateTime.now(ZoneOffset.of("+8")), "yyyyMMddHHmmssSSS"));
                protocolRepository.updateProtocolStep(protocolStep);
                templateFields.stream().filter(field -> field.getTemplateStepId().equals(step.getId()))
                        .forEach(field -> {
                            CorporationProtocolFiledEntity protocolField = new CorporationProtocolFiledEntity(supplierAndUser.getTenant());
                            protocolField.setSupplierId(supplierAndUser.getSupplier().getId());
                            protocolField.setFieldName(field.getFieldName());
                            protocolField.setProtocolId(protocol.getId());
                            protocolField.setStepId(protocolStep.getId());
                            protocolField.setSupplierCorporationId(vo.getCorporationId());
                            protocolField.setFieldName(field.getFieldName());
                            protocolField.setRelationCode(field.getFieldCode());
                            protocolField.setFieldValue(getProtocolFiledValue(protocolStep, protocolField, labor, protocol, corporation));
                            protocolRepository.saveProtocolFiled(protocolField);
                        });
            });
        });


    }

    private String format(LocalDateTime time, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return formatter.format(time);
    }

    /**
     * 签署第一步：创建待签署文件
     *
     * @param list 状态为草稿的劳务协议
     */
    public void signContract2CloudSignBiz(List<CorporationProtocolEntity> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.info("请求云签创建待签署文件-传入参数为空,不处理");
            return;
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (CorporationProtocolEntity pro : list) {
                //查询配置域字段
                CorporationProtocolEntity protocol = protocolRepository.queryProtocolById(pro.getId());
                if (protocol == null || !protocol.getSignStatus().equals(EnumContractSignStatus.DRAFT)) {
                    log.info("当前协议状态不是草稿,不处理,合同ID:{}", pro.getId());
                    continue;
                }
                List<CorporationProtocolFiledEntity> fieldList = protocolRepository.queryFieldByTempId(protocol.getId());
                List<FieldValue> fieldValues = null;
                if (CollectionUtil.isEmpty(fieldList)) {
                    log.error("合同配置填充域为空,创建字段为空的云签待签署文件,合同ID:{}", protocol.getId());
                } else {
                    fieldValues = fieldList.stream().map(f -> {
                        FieldValue fieldValue = new FieldValue();
                        fieldValue.setName(f.getFieldName());
                        fieldValue.setValue(f.getFieldValue());
                        return fieldValue;
                    }).collect(Collectors.toList());
                }
                Long signFileId = signManager.createSignFile(protocol, fieldValues);
                if (signFileId == null) {
                    //下载失败，稍后再试
                    continue;
                }
                protocol.setAgentContractId(signFileId);
                protocol.setSignStatus(EnumContractSignStatus.CREATED);
                protocolRepository.updateProtocol(protocol);
            }
        }
    }

    /**
     * 批量创建待签署文件
     */
    public void createSigningFile() {
        List<CorporationProtocolEntity> list = protocolRepository.queryProtocolByStatus(List.of(EnumContractSignStatus.DRAFT),
                LocalDateTime.now().plusHours(12),
                LocalDateTime.now());
        signContract2CloudSignBiz(list);
    }


    /**
     * 第二步：下载待签署文件并上传至文件服务器
     *
     * @param list 已创建状态的协议列表
     */
    public void signContract2Archive(List<CorporationProtocolEntity> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.info("请求云签查询创建待签署文件-传入参数为空,不处理,{}", JSONUtils.json(list));
            return;
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (CorporationProtocolEntity pro : list) {
                CorporationProtocolEntity protocol = protocolRepository.queryProtocolById(pro.getId());
                if (protocol.getSignStatus() != EnumContractSignStatus.CREATED) {
                    log.info("合同签署状态已不是已创建，不处理,合同ID:{},状态：{}", protocol.getId(), protocol.getSignStatus());
                    continue;
                }
                String archiveId = null;
                try {
                    archiveId = signManager.downloadFileWaitSign(protocol);
                    //更新合同
                    protocol.setProtocolFileId(archiveId);
                    protocol.setFileId(archiveId);
                    protocol.setSignStatus(EnumContractSignStatus.WAIT_SIGN);
                    protocolRepository.updateProtocol(protocol);
                    //更新当前进程，由初始转为第一个签署人
                    protocolManager.nextStep(protocol);
                } catch (IOException e) {
                    log.error("下载待签署文件异常,合同ID:{}", protocol.getId(), e);
                }
            }
        }
    }


    /**
     * 批量下载合同并进入签署流程
     */
    public void downloadSigningFileAndSign() {
        List<CorporationProtocolEntity> list = protocolRepository.queryProtocolByStatus(List.of(EnumContractSignStatus.CREATED),
                LocalDateTime.now().plusHours(12),
                LocalDateTime.now());
        signContract2Archive(list);
    }


    public void sign(Long protocolId, String signImage, Long userId, TemplateController.SupplierAndUser supplierAndUser) {

        PersonInfoData user = userManager.getUserInfo(userId);
        if (user == null) {
            throw new ApiException("用户不存在", ApiException.NOT_FOUND);
        }
        CorporationProtocolEntity protocol = protocolRepository.queryProtocolById(protocolId);
        if (protocol == null) {
            throw new ApiException("协议不存在", ApiException.NOT_FOUND);
        }
        if (!supplierAndUser.getSupplier().getId().equals(protocol.getSupplierId())) {
            throw new ApiException("协议不存在", ApiException.NOT_FOUND);
        }
        List<CorporationProtocolStepEntity> steps = protocolRepository.queryStepByProtocolId(protocolId);
        List<CorporationProtocolStepEntity> stepsWaitSign = steps.stream().filter(v -> v.getOperate().equals(EnumOperateType.SIGN)
                && !v.getSignStatus().equals(EnumContractSignState.SUCCESS)
        ).toList();
        if (stepsWaitSign.isEmpty()) {
            throw new ApiException("没有待签署步骤", ApiException.NOT_FOUND);
        }
        CorporationProtocolStepEntity currStep = stepsWaitSign.get(0);
        if (currStep.getOperate() != EnumOperateType.SIGN || currStep.getIdCard().equals(user.getIdCard())) {
            throw new ApiException("当前步骤不是签署步骤或签署人已签署", ApiException.NOT_FOUND);
        }
        SupplierLaborEntity labor = laborInfoRepository.findLaborByIdCard(currStep.getIdCard(), protocol.getSupplierId());
        signManager.prepareSign(signImage, labor, currStep, protocol);
        signManager.requestSign(currStep);
        currStep.setSignStatus(EnumContractSignState.IN_PROCESS);
        currStep.setSignTime(LocalDateTime.now());
        protocolRepository.updateProtocolStep(currStep);
    }





    /**
     * 查询签署状态
     */
    public void querySignStepStatus() {
        List<CorporationProtocolStepEntity> list = protocolRepository.queryStepByStatus(
                Arrays.asList(EnumContractSignState.ACCEPT
                , EnumContractSignState.IN_PROCESS), LocalDateTime.now().minusDays(1), LocalDateTime.now());
        querySignStatus(list);
    }


    public void querySignStatus(List<CorporationProtocolStepEntity> list) {
        /**
         * 补ACCEPT和IN_PROCESS状态签署记录  时间范围判断创建时间变为判断更新时间，因存在打开合同但未签署的情况，再次打开超过时间间隔则无法补单
         */
        if (CollectionUtil.isNotEmpty(list)) {
            for (CorporationProtocolStepEntity step : list) {
                try {
                    //查询合同
                    CorporationProtocolEntity protocol = protocolRepository.queryProtocolById(step.getProtocolId());
                    if(protocol == null){
                        log.info("合同已废弃,合同Id:{}",step.getProtocolId());
                        continue;
                    }
                    //查询签名状态
                    String fileId = signManager.querySignStatusAndFile(step, protocol);
                    step.setSignStatus(EnumContractSignState.SUCCESS);
                    step.setSignFileId(fileId);
                    step.setCurrentStep(false);
                    protocolRepository.updateProtocolStep(step);
                    protocolManager.nextStep(protocol);
                } catch (Exception e) {
                    log.info("电子合同补偿异常:签署记录:{},异常信息:",JSONUtils.json(step),e);
                }
            }
        }
    }



    private String getProtocolFiledValue(CorporationProtocolStepEntity protocolStep,
                                         CorporationProtocolFiledEntity protocolField,
                                         SupplierLaborEntity labor,
                                         CorporationProtocolEntity protocol,
                                         CorporationData corporation) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //个人签署方字段获取
        if (protocolStep.getOperate() == EnumOperateType.SIGN) {
            switch (protocolField.getRelationCode()) {
                case "name":
                    return labor.getName();
                case "idCard":
                    return protocolStep.getIdCard();
                case "mobile":
                    return labor.getCellphone();
                case "startDate":
                    return protocol.getStartDate().format(formatter);
                case "endDate":
                    return protocol.getEndDate().format(formatter);
            }
        }
        //公章签署方字段获取
        if (protocolStep.getOperate() == EnumOperateType.SEAL) {
            switch (protocolField.getRelationCode()) {
                case "corporationName":
                    return corporation.getName();
                case "registerAddress":
                    return corporation.getInfo().getRegisterAddress();
                case "representativeName":
                    return corporation.getInfo().getRepresentativeName();
            }
        }
        return "";
    }




}
