package com.olading.operate.labor.config;

import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class JacksonConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> builder
                .deserializerByType(String.class, new NullIfEmptyStringDeserializer())
                .deserializerByType(List.class, new NullIfEmptyInListDeserializer())
                .deserializerByType(Enum.class, new NullIfEmptyInEnumDeserializer());
    }
}