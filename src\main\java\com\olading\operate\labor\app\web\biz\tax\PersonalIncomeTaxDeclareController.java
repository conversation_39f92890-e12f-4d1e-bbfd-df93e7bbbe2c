package com.olading.operate.labor.app.web.biz.tax;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.query.PersonalIncomeTaxDeclareQuery;
import com.olading.operate.labor.domain.service.PersonalIncomeTaxDeclareService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDetailVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Tag(name = "个税申报相关接口")
@RestController
@RequestMapping("/api/supplier/personaltax")
@RequiredArgsConstructor
@Slf4j
public class PersonalIncomeTaxDeclareController extends BusinessController {

    private final PersonalIncomeTaxDeclareService personalIncomeTaxDeclareService;
    private final QueryService queryService;

    private static final String PERSONAL_INCOME_TAX_TEMPLATE = "/template/个税申报表.xls";

    @Operation(summary = "个税申报记录列表")
    @PostMapping("/list")
    public WebApiQueryResponse<PersonalIncomeTaxDeclareVo> listPersonalIncomeTaxDeclare(@RequestBody QueryFilter<WebQueryPersonalIncomeTaxDeclareFilters> request) {
        QueryFilter<PersonalIncomeTaxDeclareQuery.Filters> filter = request.convert(WebQueryPersonalIncomeTaxDeclareFilters::convert);
        filter.sort("id", Direction.DESCENDING);


        DataSet<PersonalIncomeTaxDeclareVo> ds = queryService.queryPersonalIncomeTaxDeclare(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "新增个税申报记录")
    @PostMapping("/add")
    public WebApiResponse<Long> addPersonalIncomeTaxDeclare(@Valid @RequestBody PersonalIncomeTaxDeclareParam param) {
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        BeanUtils.copyProperties(param, vo);
        vo.setSupplierId(currentSupplierId());

        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(currentTenant(), vo);
        return WebApiResponse.success(entity.getId());
    }

    @Operation(summary = "更新个税申报记录")
    @PostMapping("/update")
    public WebApiResponse<Void> updatePersonalIncomeTaxDeclare(@Valid @RequestBody PersonalIncomeTaxDeclareParam param) {
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        BeanUtils.copyProperties(param, vo);

        personalIncomeTaxDeclareService.updatePersonalIncomeTaxDeclare(currentTenant(), vo);
        return WebApiResponse.success();
    }

    @Operation(summary = "个税申报记录详情")
    @PostMapping("/query")
    public WebApiResponse<PersonalIncomeTaxDeclareVo> queryPersonalIncomeTaxDeclare(@Valid @RequestBody IdRequest request) {
        PersonalIncomeTaxDeclareVo vo = personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(request.getId());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "删除个税申报记录")
    @PostMapping("/delete")
    public WebApiResponse<Void> deletePersonalIncomeTaxDeclare(@Valid @RequestBody IdRequest request) {
        personalIncomeTaxDeclareService.deletePersonalIncomeTaxDeclare(currentTenant(), request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "更新个税申报状态为已申报")
    @PostMapping("/updateTaxStatus")
    public WebApiResponse<Void> updateTaxStatusToDeclared(@Valid @RequestBody IdRequest request) {
        personalIncomeTaxDeclareService.updateTaxStatusToDeclared(request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "个税申报下载")
    @PostMapping(value = "/download/declarationRecord")
    public void downloadDeclarationRecord(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody IdRequest param) {
        try {
            // 获取个税申报记录详情
            PersonalIncomeTaxDeclareVo vo = personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(param.getId());

            // 读取模板文件
            Workbook workbook = new HSSFWorkbook(new DefaultResourceLoader().getResource(PERSONAL_INCOME_TAX_TEMPLATE).getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 填充数据到模板
            fillTemplateData(sheet, vo);

            // 设置文件名
            String fileName = String.format("%s_个税申报表.xls", vo.getIncomeTaxMonth() != null ? vo.getIncomeTaxMonth() : "");

            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("个税申报表模板下载失败", e);
            throw new RuntimeException("个税申报表模板下载失败: " + e.getMessage());
        }
    }

    /**
     * 填充模板数据
     */
    private void fillTemplateData(Sheet sheet, PersonalIncomeTaxDeclareVo vo) {
        if (vo == null || CollectionUtils.isEmpty(vo.getDetails())) {
            return;
        }

        List<PersonalIncomeTaxDetailVo> details = vo.getDetails();

        // 从第二行开始写入数据（第一行是表头）
        int rowIndex = 1;

        for (PersonalIncomeTaxDetailVo detail : details) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // 工号列为空 (列索引0)
            createCellWithValue(row, 0, "");

            // 姓名列 (列索引1)
            createCellWithValue(row, 1, detail.getName() != null ? detail.getName() : "");

            // 证件类型列 (列索引2)
            createCellWithValue(row, 2, "居民身份证");

            // 证件号码列 (列索引3)
            createCellWithValue(row, 3, detail.getIdCard() != null ? detail.getIdCard() : "");

            // 所得项目列 (列索引4)
            createCellWithValue(row, 4, "其他连续劳务报酬");

            // 本期收入列 (列索引5)
            createCellWithValue(row, 5, detail.getCurrentIncome() != null ? detail.getCurrentIncome().toString() : "");

            // 本期免税收入列 (列索引6) - 为空
            createCellWithValue(row, 6, "");

            // 累计个人养老金列 (列索引7) - 为空
            createCellWithValue(row, 7, "");

            // 商业健康保险列 (列索引8) - 为空
            createCellWithValue(row, 8, "");

            // 税延养老保险列 (列索引9) - 为空
            createCellWithValue(row, 9, "");

            // 其他列 (列索引10) - 为空
            createCellWithValue(row, 10, "");

            // 允许扣除的税费列 (列索引11) - 为空
            createCellWithValue(row, 11, "");

            // 减免税额列 (列索引12)
            createCellWithValue(row, 12, detail.getAccumulatedTaxReductions() != null ? detail.getAccumulatedTaxReductions().toString() : "");

            // 备注列 (列索引13) - 为空
            createCellWithValue(row, 13, "");

            rowIndex++;
        }
    }

    /**
     * 创建单元格并设置值
     */
    private void createCellWithValue(Row row, int columnIndex, String value) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        cell.setCellValue(value != null ? value : "");
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "个税申报记录ID不能为空")
        @Schema(description = "个税申报记录ID")
        private Long id;
    }

    @Data
    public static class SupplierCorporationRequest {
        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;
    }

    @Data
    public static class WebQueryPersonalIncomeTaxDeclareFilters {
        @Schema(description = "个税申报记录ID")
        private Long id;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        private String taxPaymentPeriod;

        @Schema(description = "个税申报月")
        private String incomeTaxMonth;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        @Schema(description = "生成状态")
        private String status;

        @Schema(description = "申报状态")
        private String taxStatus;

        @Schema(description = "创建时间-起")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDateTime createTimeEnd;

        public PersonalIncomeTaxDeclareQuery.Filters convert() {
            return Beans.copyBean(this, PersonalIncomeTaxDeclareQuery.Filters.class);
        }
    }

    @Data
    public static class PersonalIncomeTaxDeclareParam {
        @Schema(description = "个税申报记录ID")
        private Long id;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        private String taxPaymentPeriod;

        @Schema(description = "个税申报月")
        private String incomeTaxMonth;

        @Schema(description = "纳税人数")
        private String taxpayersCount;

        @Schema(description = "本期收入")
        private String currentIncome;
    }
}
