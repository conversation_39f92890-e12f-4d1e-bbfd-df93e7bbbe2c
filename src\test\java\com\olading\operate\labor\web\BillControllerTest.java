package com.olading.operate.labor.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.app.web.biz.bill.BillController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.bill.BillMasterStatus;
import com.olading.operate.labor.domain.bill.dto.BillGenerateRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportResult;
import com.olading.operate.labor.domain.bill.vo.*;
import com.olading.operate.labor.domain.query.BillQuery;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.service.BillService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 账单控制器完整测试
 * 覆盖所有API接口，包括账单生成、状态管理、查询和导入功能
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class BillControllerTest extends BaseTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private BillService billService;

    @Autowired
    private QueryService queryService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    // 测试数据
    private Long testSupplierId;
    private Long testCustomerId;
    private Long testCorporationId;
    private Long testContractId;
    private Long testSalaryStatementId;
    private LocalDate testBillMonth;
    @Autowired
    private BusinessContractManager businessContractManager;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        testBillMonth = LocalDate.of(2025, 7, 1);
        
        // 准备测试数据
        withTransaction(status -> {
            setupTestData();
            return null;
        });
    }

    /**
     * 准备测试数据：创建供应商、客户、合同、薪酬数据等
     */
    private void setupTestData() {

        final ContractVo contractVo = businessContractManager.queryContract(16L);


        testSupplierId = 9L;
        testCustomerId =contractVo.getCustomerId();
        testCorporationId = contractVo.getSupplierCorporationId();
        testContractId = contractVo.getId();


        // 4. 创建薪酬批次数据
        testSalaryStatementId = createSalaryStatement();

        // 5. 创建薪酬明细数据
        createSalaryDetails(testSalaryStatementId, 5); // 创建5个人的薪酬明细

        em.flush();
    }

    /**
     * 创建薪酬批次数据
     */
    private Long createSalaryStatement() {
        SalaryStatementEntity salaryStatement = new SalaryStatementEntity(TenantInfo.ofSupplier(testSupplierId));
        salaryStatement.setCustomerId(testCustomerId);
        salaryStatement.setContractId(testContractId);
        salaryStatement.setSupplierId(testSupplierId);
        salaryStatement.setSupplierCorporationId(testCorporationId);
        salaryStatement.setTaxPeriod(testBillMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        salaryStatement.setTotalPeople(5L);
        salaryStatement.setTotalPayable(new BigDecimal("50000.00"));
        salaryStatement.setTotalIncomeTax(new BigDecimal("2500.00"));
        salaryStatement.setTotalVat(new BigDecimal("1500.00"));
        salaryStatement.setTotalSurtax(new BigDecimal("150.00"));
        salaryStatement.setNetPaymentTotal(new BigDecimal("45850.00"));
        salaryStatement.setTaxDeclarationMonth(testBillMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        salaryStatement.setStatus(SalaryStatementStatus.CONFIRMED);
        salaryStatement.setUploadTime(LocalDateTime.now());
        
        em.persist(salaryStatement);
        return salaryStatement.getId();
    }

    /**
     * 创建薪酬明细数据
     */
    private void createSalaryDetails(Long salaryStatementId, int count) {
        for (int i = 1; i <= count; i++) {
            SalaryDetailEntity detail = new SalaryDetailEntity(TenantInfo.ofSupplier(testSupplierId));
            detail.setSalaryStatementId(salaryStatementId);
            detail.setName("测试员工" + i);
            detail.setIdCard("11010119900101123" + i);
            detail.setPhoneNumber("1380000000" + i);
            detail.setPayableAmount(new BigDecimal("10000.00"));
            detail.setAccumulatedIncome(new BigDecimal("10000.00"));
            detail.setCurrentWithholdingTax(new BigDecimal("500.00"));
            detail.setVatAmount(new BigDecimal("300.00"));
            detail.setAdditionalTaxAmount(new BigDecimal("30.00"));
            detail.setNetPayment(new BigDecimal("9170.00"));
            
            em.persist(detail);
        }
    }

    // ==================== API接口测试 ====================

    @Test
    @DisplayName("1. 生成账单 - 成功场景")
    void generateBill_Success() throws Exception {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);
        request.setRemark("测试生成账单");

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.contractId").value(testContractId))
                .andExpect(jsonPath("$.data.billMonth").value(testBillMonth.toString()))
                .andExpect(jsonPath("$.data.billStatus").value("GENERATED"))
                .andExpect(jsonPath("$.data.salaryAmount").value(50000.00))
                .andExpect(jsonPath("$.data.totalReceivableAmount").exists());
    }

    @Test
    @DisplayName("2. 生成账单 - 参数验证失败")
    void generateBill_ValidationFailed() throws Exception {
        // Given - 缺少必需参数
        BillGenerateRequest request = new BillGenerateRequest();
        // 不设置contractId和billMonth

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("3. 提交账单确认 - 成功场景")
    void submitBillForConfirm_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/{billId}/submit", billId)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证账单状态已更新
        BillMasterVO bill = billService.getBillDetail(testSupplierId, billId);
        assertThat(bill.getBillStatus()).isEqualTo(BillMasterStatus.PENDING_CONFIRM);
    }

    @Test
    @DisplayName("4. 确认账单 - 成功场景")
    void confirmBill_Success() throws Exception {
        // Given - 先生成账单并提交确认
        Long billId = createTestBill();
        billService.submitBillForConfirm(testSupplierId, billId);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/{billId}/confirm", billId)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证账单状态已更新
        BillMasterVO bill = billService.getBillDetail(testSupplierId, billId);
        assertThat(bill.getBillStatus()).isEqualTo(BillMasterStatus.CONFIRMED);
        assertThat(bill.getConfirmTime()).isNotNull();
    }

    @Test
    @DisplayName("5. 删除账单 - 成功场景")
    void deleteBill_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        // When & Then
        mockMvc.perform(delete("/api/supplier/bills/{billId}", billId)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @DisplayName("6. 获取账单详情 - 成功场景")
    void getBillDetail_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        // When & Then
        mockMvc.perform(get("/api/supplier/bills/{billId}", billId)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(billId))
                .andExpect(jsonPath("$.data.contractId").value(testContractId))
                .andExpect(jsonPath("$.data.customerId").value(testCustomerId))
                .andExpect(jsonPath("$.data.billStatus").value("GENERATED"))
                .andExpect(jsonPath("$.data.categories").isArray());
    }

    @Test
    @DisplayName("7. 分页查询账单列表 - 成功场景")
    void queryBills_Success() throws Exception {
        // Given - 先生成几个账单
        createTestBill();
        createTestBill();

        BillController.WebBillFilters filters = new BillController.WebBillFilters();
        filters.setContractId(testContractId);
        filters.setBillStatus(BillMasterStatus.GENERATED);

        QueryFilter<BillController.WebBillFilters> request = new QueryFilter<>();
        request.setFilters(filters);
        request.setLimit(10L);
        request.setStart(1L);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.total").exists())
                .andExpect(jsonPath("$.total").value(2));
    }

    @Test
    @DisplayName("8. 获取账单薪酬明细 - 成功场景")
    void getBillSalaryDetails_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        BillController.WebBillSalaryDetailFilters filters = new BillController.WebBillSalaryDetailFilters();
        filters.setBillMasterId(billId);

        QueryFilter<BillController.WebBillSalaryDetailFilters> request = new QueryFilter<>();
        request.setFilters(filters);
        request.setLimit(10L);
        request.setStart(1L);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/salary-details")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.total").value(5)); // 5个薪酬明细
    }

    @Test
    @DisplayName("9. 获取账单管理费明细 - 成功场景")
    void getBillManagementFeeDetails_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        BillController.WebBillManagementFeeDetailFilters filters = new BillController.WebBillManagementFeeDetailFilters();
        filters.setBillMasterId(billId);

        QueryFilter<BillController.WebBillManagementFeeDetailFilters> request = new QueryFilter<>();
        request.setFilters(filters);
        request.setLimit(10L);
        request.setStart(1L);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/management-fee-details")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("10. 获取账单其他费用明细 - 成功场景")
    void getBillOtherFeeDetails_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        BillController.WebBillOtherFeeDetailFilters filters = new BillController.WebBillOtherFeeDetailFilters();
        filters.setBillMasterId(billId);

        QueryFilter<BillController.WebBillOtherFeeDetailFilters> request = new QueryFilter<>();
        request.setFilters(filters);
        request.setLimit(10L);
        request.setStart(1L);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/other-fee-details")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("11. 下载其他费用导入模板 - 成功场景")
    void downloadOtherFeeImportTemplate_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/supplier/bills/other-fees/import/template")
                .header("X-Supplier-Id", testSupplierId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .andExpect(header().exists("Content-Disposition"));
    }

    @Test
    @DisplayName("12. 导入其他费用数据 - 成功场景")
    void previewOtherFeeImport_Success() throws Exception {
        // Given - 先生成一个账单
        Long billId = createTestBill();

        // 创建测试Excel文件
        MockMultipartFile file = createTestExcelFile();

        // When & Then
        mockMvc.perform(multipart("/api/supplier/bills/{billId}/other-fees/import/preview", billId)
                .file(file)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").exists())
                .andExpect(jsonPath("$.data.importCount").exists());
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试账单
     */
    private Long createTestBill() {
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);
        request.setRemark("测试账单");

        BillMasterVO bill = billService.generateBill(
            TenantInfo.ofSupplier(testSupplierId), 
            testSupplierId, 
            request
        );
        return bill.getId();
    }

    /**
     * 创建测试Excel文件
     */
    private MockMultipartFile createTestExcelFile() {
        // 创建简单的CSV格式作为Excel文件测试
        String csvContent = "费用名称,费用金额,产生时间,产生人,身份证号,费用用途\n" +
                           "交通费,150.00,2025-07-15,张三,110101199001011234,出差交通费\n" +
                           "餐费,80.50,2025-07-16,李四,110101199002021234,加班餐费";
        
        return new MockMultipartFile(
            "file",
            "test-other-fees.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            csvContent.getBytes()
        );
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    @DisplayName("13. 生成账单 - 合同不存在")
    void generateBill_ContractNotFound() throws Exception {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(999999L); // 不存在的合同ID
        request.setBillMonth(testBillMonth);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @DisplayName("14. 提交账单确认 - 账单不存在")
    void submitBillForConfirm_BillNotFound() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/supplier/bills/{billId}/submit", 999999L)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @DisplayName("15. 确认账单 - 状态不正确")
    void confirmBill_InvalidStatus() throws Exception {
        // Given - 生成账单但不提交确认
        Long billId = createTestBill();

        // When & Then - 直接确认应该失败
        mockMvc.perform(post("/api/supplier/bills/{billId}/confirm", billId)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @DisplayName("16. 删除账单 - 已确认状态不能删除")
    void deleteBill_ConfirmedBillCannotDelete() throws Exception {
        // Given - 生成并确认账单
        Long billId = createTestBill();
        billService.submitBillForConfirm(testSupplierId, billId);
        billService.confirmBill(testSupplierId, billId);

        // When & Then - 删除已确认的账单应该失败
        mockMvc.perform(delete("/api/supplier/bills/{billId}", billId)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @DisplayName("17. 权限测试 - 无权限访问其他供应商的账单")
    void getBillDetail_NoPermission() throws Exception {
        // Given - 创建另一个供应商的账单
        Long otherSupplierId = uniqueId();
        Long billId = createTestBill();

        // When & Then - 用其他供应商ID访问应该失败
        mockMvc.perform(get("/api/supplier/bills/{billId}", billId)
                .header("X-Supplier-Id", otherSupplierId)
                .header("X-Tenant-Id", "supplier:" + otherSupplierId))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("18. 导入其他费用 - 文件格式错误")
    void previewOtherFeeImport_InvalidFileFormat() throws Exception {
        // Given
        Long billId = createTestBill();
        MockMultipartFile invalidFile = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "invalid content".getBytes()
        );

        // When & Then
        mockMvc.perform(multipart("/api/supplier/bills/{billId}/other-fees/import/preview", billId)
                .file(invalidFile)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("19. 查询账单列表 - 复杂查询条件")
    void queryBills_ComplexFilters() throws Exception {
        // Given - 创建多个账单
        createTestBill();
        createTestBill();

        BillController.WebBillFilters filters = new BillController.WebBillFilters();
        filters.setCustomerId(testCustomerId);
        filters.setBillMonthStart(testBillMonth.minusDays(1));
        filters.setBillMonthEnd(testBillMonth.plusDays(1));
        filters.setBillStatus(BillMasterStatus.GENERATED);

        QueryFilter<BillController.WebBillFilters> request = new QueryFilter<>();
        request.setFilters(filters);
        request.setLimit(10L);
        request.setStart(1L);

        // When & Then
        mockMvc.perform(post("/api/supplier/bills/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.total").value(2));
    }

    @Test
    @DisplayName("20. 账单生成 - 无可用薪酬数据")
    void generateBill_NoAvailableSalaryData() throws Exception {
        // Given - 创建一个没有薪酬数据的合同
        Long newContractId = uniqueId();
        BusinessContractEntity newContract = new BusinessContractEntity();
        newContract.setId(newContractId);
        newContract.setSupplierId(testSupplierId);
        newContract.setCustomerId(testCustomerId);
        newContract.setSupplierCorporationId(testCorporationId);
        newContract.setName("无薪酬数据合同");
        newContract.setStopped(false);
        
        withTransaction(status -> {
            em.persist(newContract);
            em.flush();
            return null;
        });

        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(newContractId);
        request.setBillMonth(testBillMonth);

        // When & Then - 应该能生成账单，但金额为0
        mockMvc.perform(post("/api/supplier/bills/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Supplier-Id", testSupplierId)
                .header("X-Tenant-Id", "supplier:" + testSupplierId)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.salaryAmount").value(0.00));
    }
}