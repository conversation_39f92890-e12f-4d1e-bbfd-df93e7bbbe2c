server.port=8120
management.endpoints.web.exposure.include=*
management.prometheus.metrics.export.enabled=true


spring.application.name=olading-operate-labor



spring.cache.type=simple

spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB
spring.mvc.hiddenmethod.filter.enabled=false



# datasource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=***********************************************************************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=100

spring.data.jdbc.repositories.enabled=false
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.generate-ddl=false
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.jdbc.batch_size=100
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

spring.data.redis.host=*************
spring.data.redis.database=12
spring.data.redis.port=6379


app.master-key=cf8080b16f1d4e24b89f09cf2734111e
olading.master-key=cf8080b16f1d4e24b89f09cf2734111e
spring.cloud.discovery.client.simple.instances.lanmaoly-cloud-api-gateway[0].uri=http://*************:8013
spring.cloud.discovery.client.simple.instances.lanmaoly-cloud-archive-service[0].uri=http://*************:8002
spring.cloud.discovery.client.simple.instances.olading-basic-info-service[0].uri=http://*************:8049
spring.cloud.discovery.client.simple.instances.olading-psalary-di[0].uri=http://*************:8045

knife4j.enable=true
logging.level.org.springframework=DEBUG


app.signing-api-url=https://46-dev-api.olading.com/api/signing/v1
app.signing-tenant-id=80
app.signing-client-key=e7b2f02190664d6583cd1e005a3dc460
app.signing-secret-key=e01bfdd100684456b094a95d549bc967
app.signing-temp-uploadUrl=https://46-qa.olading.com/gd/hrsaas/webapi/api/signing/template/upload
