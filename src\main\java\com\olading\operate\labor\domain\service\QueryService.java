package com.olading.operate.labor.domain.service;

import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.QueryFactory;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.proxy.ProxyOrderBatchDetailData;
import com.olading.operate.labor.domain.proxy.ProxyOrderData;
import com.olading.operate.labor.domain.query.*;
import com.olading.operate.labor.domain.salary.vo.PreviousIncomeDeductionVO;
import com.olading.operate.labor.domain.salary.vo.SalaryVO;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTemplateEntity;
import com.olading.operate.labor.domain.share.task.TaskEntity;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDetailManager;
import com.olading.operate.labor.domain.share.tax.ValueAddedTaxDetailManager;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.TaxPaymentVoucherVo;
import com.olading.operate.labor.domain.share.user.UserData;
import com.olading.operate.labor.domain.supplier.SupplierCustomerData;
import com.olading.operate.labor.domain.supplier.SupplierData;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Transactional
@Component
public class QueryService {

    private final QueryFactory queryFactory;
    private final EntityManager em;
    private final PersonalIncomeTaxDetailManager personalIncomeTaxDetailManager;
    private final ValueAddedTaxDetailManager valueAddedTaxDetailManager;


    public DataSet<SupplierCustomerData> querySupplierCustomer(QueryFilter<SupplierCustomerQuery.Filters> request) {
        return queryFactory.create(new SupplierCustomerQuery()).execute(request);
    }

    public DataSet<SupplierData> querySupplier(QueryFilter<SupplierQuery.Filters> request) {
        return queryFactory.create(new SupplierQuery()).execute(request);
    }


    /**
     * 查询用户信息
     */
    public DataSet<UserData> queryUser(QueryFilter<UserQuery.Filters> request) {
        return queryFactory.create(new UserQuery()).execute(request);
    }

    /**
     * 查询角色
     */
    public DataSet<RoleData> queryRole(QueryFilter<RoleQuery.Filters> request) {
        return queryFactory.create(new RoleQuery()).execute(request);
    }


    public DataSet<RoleMemberQuery.Record> queryRoleMember(QueryFilter<RoleMemberQuery.Filters> request) {
        return queryFactory.create(new RoleMemberQuery()).execute(request);
    }

    public DataSet<SupplierMemberQuery.Record> queryMember(QueryFilter<SupplierMemberQuery.Filters> request) {
        return queryFactory.create(new SupplierMemberQuery()).execute(request);
    }

    public DataSet<TaskEntity> queryOrderDetailTask(QueryFilter<OrderDetailTaskQuery.Filters> request) {
        return queryFactory.create(new OrderDetailTaskQuery()).execute(request);
    }

    /**
     * 查询列表
     */
    public DataSet<CorporationProtocolTemplateEntity> queryProtocolTemplate(QueryFilter<ProtocolTemplateQuery.Filters> request) {
        return queryFactory.create(new ProtocolTemplateQuery()).execute(request);
    }
    public DataSet<ProtocolQuery.Record> queryProtocol(QueryFilter<ProtocolQuery.Filters> request) {
        return queryFactory.create(new ProtocolQuery()).execute(request);
    }

    public DataSet<SupplierCorporationEntity> queryCorporation(QueryFilter<SupplierCorporationQuery.Filters> request) {
        return queryFactory.create(new SupplierCorporationQuery()).execute(request);
    }

    public DataSet<LaborQuery.Record> queryLabor(QueryFilter<LaborQuery.Filters> request) {
        return queryFactory.create(new LaborQuery()).execute(request);
    }


    /**
     * 客户查询
     */
    public DataSet<CustomerWithInfo> queryCustomer(QueryFilter<CustomerQuery.Filters> request) {
        return queryFactory.create(new CustomerQuery()).execute(request);
    }

    /**
     * 服务合同查询
     */
    public DataSet<ContractVo> queryContract(QueryFilter<BusinessContractQuery.Filters> request) {
        return queryFactory.create(new BusinessContractQuery()).execute(request);
    }

    /**
     * 代发订单查询
     */
    public DataSet<ProxyOrderData> queryProxyOrder(QueryFilter<ProxyOrderQuery.Filters> request) {
        return queryFactory.create(new ProxyOrderQuery()).execute(request);
    }

    /**
     * 代发批次详情
     */
    public DataSet<ProxyOrderBatchDetailData> queryProxyOrderBatch(QueryFilter<ProxyBatchQuery.Filters> request) {
        return queryFactory.create(new ProxyBatchQuery()).execute(request);
    }

    /**
     * 个税申报记录查询
     */
    public DataSet<PersonalIncomeTaxDeclareVo> queryPersonalIncomeTaxDeclare(QueryFilter<PersonalIncomeTaxDeclareQuery.Filters> request) {
        DataSet<PersonalIncomeTaxDeclareVo> dataSet = queryFactory.create(new PersonalIncomeTaxDeclareQuery()).execute(request);

        // 为每个申报记录补充本期应预扣预缴税额统计
        for (PersonalIncomeTaxDeclareVo vo : dataSet.getData()) {
            if (vo.getId() != null) {
                // 根据申报ID统计本期应预扣预缴税额
                java.math.BigDecimal currentWithholdingTax = personalIncomeTaxDetailManager.sumCurrentWithholdingTaxByTaxDeclareId(vo.getId());
                vo.setCurrentWithholdingTax(currentWithholdingTax);
            }
        }

        return dataSet;
    }

    /**
     * 增值税申报记录查询
     */
    public DataSet<ValueAddedTaxDeclareVo> queryValueAddedTaxDeclare(QueryFilter<ValueAddedTaxDeclareQuery.Filters> request) {
        DataSet<ValueAddedTaxDeclareVo> dataSet = queryFactory.create(new ValueAddedTaxDeclareQuery()).execute(request);

        // 为每个申报记录补充增值税总额和附加税总额统计
        for (ValueAddedTaxDeclareVo vo : dataSet.getData()) {
            if (vo.getId() != null) {
                // 根据申报ID统计增值税总额
                java.math.BigDecimal totalVatAmount = valueAddedTaxDetailManager.sumVatAmountByValueAddedTaxId(vo.getId());
                vo.setTotalVatAmount(totalVatAmount);

                // 根据申报ID统计附加税总额
                java.math.BigDecimal totalSurtaxAmount = valueAddedTaxDetailManager.sumSurtaxAmountByValueAddedTaxId(vo.getId());
                vo.setTotalSurtaxAmount(totalSurtaxAmount);
            }
        }

        return dataSet;
    }

    /**
     * 税务缴纳凭证记录查询
     */
    public DataSet<TaxPaymentVoucherVo> queryTaxPaymentVoucher(QueryFilter<TaxPaymentVoucherQuery.Filters> request) {
        return queryFactory.create(new TaxPaymentVoucherQuery()).execute(request);
    }

    /**
     * 薪酬计算列表查询
     */
    public DataSet<SalaryVO> querySalary(QueryFilter<SalaryQuery.Filters> request) {
        return queryFactory.create(new SalaryQuery()).execute(request);
    }

    /**
     * 上期收入减除导入列表查询
     */
    public DataSet<PreviousIncomeDeductionVO> queryPreviousIncomeDeduction(QueryFilter<PreviousIncomeDeductionQuery.Filters> request) {
        return queryFactory.create(new PreviousIncomeDeductionQuery()).execute(request);
    }

    /**
     * 账单列表查询
     */
    public DataSet<com.olading.operate.labor.domain.bill.vo.BillMasterVO> queryBill(QueryFilter<BillQuery.Filters> request) {
        return queryFactory.create(new BillQuery()).execute(request);
    }

    /**
     * 账单薪酬明细查询
     */
    public DataSet<com.olading.operate.labor.domain.bill.vo.BillSalaryDetailVO> queryBillSalaryDetail(QueryFilter<BillSalaryDetailQuery.Filters> request) {
        return queryFactory.create(new BillSalaryDetailQuery()).execute(request);
    }

    /**
     * 账单管理费明细查询
     */
    public DataSet<com.olading.operate.labor.domain.bill.vo.BillManagementFeeDetailVO> queryBillManagementFeeDetail(QueryFilter<BillManagementFeeDetailQuery.Filters> request) {
        return queryFactory.create(new BillManagementFeeDetailQuery()).execute(request);
    }

    /**
     * 账单其他费用明细查询
     */
    public DataSet<com.olading.operate.labor.domain.bill.vo.BillOtherFeeDetailVO> queryBillOtherFeeDetail(QueryFilter<BillOtherFeeDetailQuery.Filters> request) {
        return queryFactory.create(new BillOtherFeeDetailQuery()).execute(request);
    }


    /**
     * 开票申请查询
     */
    public DataSet<com.olading.operate.labor.domain.invoice.vo.InvoiceVO> queryInvoice(QueryFilter<com.olading.operate.labor.domain.query.InvoiceQuery.Filters> request) {
        return queryFactory.create(new com.olading.operate.labor.domain.query.InvoiceQuery()).execute(request);
    }

    /**
     * 人员信息报送记录查询
     */
    public DataSet<com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborVo> queryInfoSubmissionLabor(QueryFilter<com.olading.operate.labor.domain.query.InfoSubmissionLaborQuery.Filters> request) {
        return queryFactory.create(new com.olading.operate.labor.domain.query.InfoSubmissionLaborQuery()).execute(request);
    }

    /**
     * 人员收入信息报送记录查询
     */
    public DataSet<com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborIncomeVo> queryInfoSubmissionLaborIncome(QueryFilter<com.olading.operate.labor.domain.query.InfoSubmissionLaborIncomeQuery.Filters> request) {
        return queryFactory.create(new com.olading.operate.labor.domain.query.InfoSubmissionLaborIncomeQuery()).execute(request);
    }

    /**
     * 企业信息报送记录查询
     */
    public DataSet<com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionEnterpriseVo> queryInfoSubmissionEnterprise(QueryFilter<com.olading.operate.labor.domain.query.InfoSubmissionEnterpriseQuery.Filters> request) {
        return queryFactory.create(new com.olading.operate.labor.domain.query.InfoSubmissionEnterpriseQuery()).execute(request);
    }

}
