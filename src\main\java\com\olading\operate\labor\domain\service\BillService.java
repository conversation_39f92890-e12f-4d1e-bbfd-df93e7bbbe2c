package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.bill.*;
import com.olading.operate.labor.domain.bill.dto.*;
import com.olading.operate.labor.domain.bill.repository.BillCategoryRepository;
import com.olading.operate.labor.domain.bill.repository.BillOtherFeeDetailRepository;
import com.olading.operate.labor.domain.bill.vo.BillCategoryVO;
import com.olading.operate.labor.domain.bill.vo.BillMasterVO;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.excel.ExcelReader;
import com.olading.operate.labor.util.excel.ExcelResult;
import com.olading.operate.labor.util.excel.ExcelWriter;
import com.olading.operate.labor.util.excel.Excels;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.share.contract.BusinessContractConfigEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.CustomerManager;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 账单服务 - 版本
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BillService {

    private final BillManager billManager;
    private final EntityManager em;
    private final BusinessContractManager businessContractManager;
    private final SalaryManager salaryManager;
    private final CustomerManager customerManager;
    private final BillOtherFeeImportValidator importValidator;

    /**
     * 生成账单
     */
    public BillMasterVO generateBill(TenantInfo tenantInfo, Long supplierId, BillGenerateRequest request) {
        log.info("生成账单, tenantId: {}, supplierId: {}, request: {}",
                tenantInfo.toTenantId(), supplierId, request);
        // 1. 验证请求参数
        validateGenerateRequest(request);

        // 2. 验证供应商ID
        validateSupplierId(supplierId);

        // 3. 从合同获取所有必要信息（客户、作业主体）
        BusinessContractEntity contractInfo = getContractBillInfo(request.getContractId(), supplierId);

        // 4. 检查是否存在未确认的账单 TODO

        // 5. 获取合同配置
        BusinessContractConfigEntity contractConfig = getContractConfig(request.getContractId());

        // 6. 自动获取合同下所有可用的薪酬数据
        List<SalaryDetailEntity> availableSalaryData = getAvailableSalaryDataByContract(contractInfo, request.getBillMonth());

        // 7. 生成并创建账单
        BillMasterEntity billMaster = billManager.generateBill(tenantInfo, supplierId, request, contractInfo, contractConfig, availableSalaryData);

        return convertToBillMasterVO(billMaster);
    }

    /**
     * 从合同获取所有必要的业务信息
     */
    private BusinessContractEntity getContractBillInfo(Long contractId, Long supplierId) {
        // 1. 获取合同信息
        BusinessContractEntity contract = em.find(BusinessContractEntity.class, contractId);
        if (contract == null || contract.isDeleted()) {
            throw new BusinessException("合同不存在或已删除");
        }

        // 2. 验证合同是否属于当前供应商
        if (!contract.getSupplierId().equals(supplierId)) {
            throw new BusinessException(String.format("合同[%s]不属于当前供应商[%d]，无权限操作",
                    contract.getName(), supplierId));
        }

        // 3. 验证合同状态
        if (contract.getStopped() != null && contract.getStopped()) {
            throw new BusinessException(String.format("合同[%s]已停用，无法生成账单", contract.getName()));
        }

        // 4. 验证客户信息
        CustomerEntity customer = em.find(CustomerEntity.class, contract.getCustomerId());
        if (customer == null || customer.isDeleted()) {
            throw new BusinessException(String.format("合同[%s]关联的客户不存在或已删除", contract.getName()));
        }

        if (customer.getDisabled() != null && customer.getDisabled()) {
            throw new BusinessException(String.format("合同[%s]关联的客户[%s]已禁用", contract.getName(), customer.getName()));
        }

        // 5. 验证作业主体信息
        SupplierCorporationEntity corporation = em.find(SupplierCorporationEntity.class,
                contract.getSupplierCorporationId());
        if (corporation == null || corporation.isDeleted()) {
            throw new BusinessException(String.format("合同[%s]关联的作业主体不存在或已删除", contract.getName()));
        }

        if (corporation.getDisabled() != null && corporation.getDisabled()) {
            throw new BusinessException(String.format("合同[%s]关联的作业主体[%s]已禁用",
                    contract.getName(), corporation.getName()));
        }

        // 6. 验证作业主体是否属于当前供应商
        if (!corporation.getSupplierId().equals(supplierId)) {
            throw new BusinessException(String.format("合同[%s]关联的作业主体[%s]不属于当前供应商",
                    contract.getName(), corporation.getName()));
        }

        // 7. 返回合同相关的所有信息
        return contract;
    }

    /**
     * 获取合同配置
     */
    private BusinessContractConfigEntity getContractConfig(Long contractId) {
        final BusinessContractConfigEntity configByContractId = businessContractManager.getConfigByContractId(contractId);
        if (configByContractId == null) {
            throw new BusinessException("合同配置不存在");
        }
        return configByContractId;
    }


    /**
     * 获取合同下所有符合条件的薪酬批次
     */
    private List<SalaryDetailEntity> getAvailableSalaryDataByContract(BusinessContractEntity contractInfo, java.time.LocalDate billMonth) {

        // 1. 获取合同下的所有薪酬批次
        List<SalaryStatementEntity> contractSalaryBatches = getSalaryBatchesByContract(contractInfo, billMonth);

        // 2. 过滤已被使用的批次
        List<Long> usedBatchIds = getUsedSalaryBatchIds(contractInfo, billMonth);

        // 3. 获取可用批次的明细数据
        List<SalaryDetailEntity> availableDetails = new ArrayList<>();
        for (SalaryStatementEntity batch : contractSalaryBatches) {
            if (!usedBatchIds.contains(batch.getId()) &&
                    isBatchStatusValid(batch.getStatus())) {
                List<SalaryDetailEntity> batchDetails = getSalaryDetailsByBatch(batch.getId());
                availableDetails.addAll(batchDetails);
            }
        }

        log.info("获取可用薪酬数据完成, 合同: {}, 客户: {}, 总批次: {}, 可用批次: {}, 明细条数: {}",
                contractInfo.getId(), contractInfo.getCustomerId(),
                contractSalaryBatches.size(), contractSalaryBatches.size() - usedBatchIds.size(),
                availableDetails.size());

        return availableDetails;
    }

    /**
     * 获取合同下的薪酬批次
     */
    private List<SalaryStatementEntity> getSalaryBatchesByContract(BusinessContractEntity contractInfo, java.time.LocalDate billMonth) {

        return salaryManager.querySalaryStatement(t->t.supplierId.eq(contractInfo.getSupplierId())
                .and(t.contractId.eq(contractInfo.getId()).and(t.taxPeriod.eq(billMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")))).and(t.deleted.eq(false))
                        .and(t.status.in(SalaryStatementStatus.CONFIRMED)))).fetch();
    }

    /**
     * 获取已使用的薪酬批次ID
     */
    private List<Long> getUsedSalaryBatchIds(BusinessContractEntity contractInfo, java.time.LocalDate billMonth) {
        return billManager.getUsedSalaryBatchIds(contractInfo, billMonth);
    }

    /**
     * 检查批次状态是否有效
     */
    private boolean isBatchStatusValid(SalaryStatementStatus status) {
        return status == SalaryStatementStatus.CONFIRMED;
    }

    /**
     * 获取薪酬批次的明细数据
     */
    private List<SalaryDetailEntity> getSalaryDetailsByBatch(Long batchId) {
        return salaryManager.querySalaryDetailByStatementId(batchId);
    }

    /**
     * 确认账单
     */
    public void confirmBill(Long supplierId, Long billId) {
        log.info("确认账单, supplierId: {}, billId: {}", supplierId, billId);
        billManager.confirmBill(supplierId, billId);
    }

    /**
     * 提交账单确认
     */
    public void submitBillForConfirm(Long supplierId, Long billId) {
        log.info("提交账单确认, supplierId: {}, billId: {}", supplierId, billId);
        billManager.submitBillForConfirm(supplierId,billId);
    }

    /**
     * 删除账单
     */
    public void deleteBill(Long supplierId, Long billId) {
        log.info("删除账单, supplierId: {}, billId: {}", supplierId, billId);
        billManager.deleteBill(supplierId,billId);
    }

    /**
     * 获取账单详情
     */
    public BillMasterVO getBillDetail(Long supplierId, Long billId) {
        log.info("获取账单详情, supplierId: {}, billId: {}", supplierId, billId);

        BillMasterEntity billMaster = billManager.getBillWithPermissionCheck(supplierId, billId);

        BillMasterVO billMasterVO = convertToBillMasterVO(billMaster);
        final CustomerWithInfo customerWithInfo = customerManager.queryCustomer(billMaster.getCustomerId());
        billMasterVO.setCustomerName(customerWithInfo.getName());
        final ContractVo contractVo = businessContractManager.queryContract(billMaster.getContractId());
        billMasterVO.setContractName(contractVo.getName());

        // 获取账单分类统计
        List<BillCategoryEntity> categories = billManager.getCategoriesByBillId(billId);

        List<BillCategoryVO> categoryVOs = categories.stream()
                .map(this::convertToBillCategoryVO)
                .toList();
        billMasterVO.setCategories(categoryVOs);

        return billMasterVO;
    }

    /**
     * 验证请求参数
     */
    private void validateGenerateRequest(BillGenerateRequest request) {
        if (request.getContractId() == null) {
            throw new BusinessException("合同ID不能为空");
        }
        if (request.getBillMonth() == null) {
            throw new BusinessException("账单月份不能为空");
        }

        // 验证账单月份不能是未来月份
        java.time.LocalDate now = java.time.LocalDate.now();
        if (request.getBillMonth().isAfter(now.withDayOfMonth(1))) {
            throw new BusinessException("不能为未来月份生成账单");
        }
    }

    /**
     * 验证供应商ID
     */
    private void validateSupplierId(Long supplierId) {
        if (supplierId == null) {
            throw new BusinessException("供应商ID不能为空");
        }

        // 注意：这里可以添加供应商存在性验证，但需要根据实际的Supplier实体类调整
        // 暂时跳过供应商验证，因为在合同验证时会间接验证
    }


    /**
     * 分页查询账单列表
     */
    private BillMasterVO convertToBillMasterVO(BillMasterEntity billMaster) {
        BillMasterVO vo = new BillMasterVO();
        vo.setId(billMaster.getId());
        vo.setBillNo(billMaster.getBillNo());
        vo.setSupplierId(billMaster.getSupplierId());
        vo.setCustomerId(billMaster.getCustomerId());
        vo.setSupplierCorporationId(billMaster.getSupplierCorporationId());
        vo.setContractId(billMaster.getContractId());
        vo.setBillMonth(billMaster.getBillMonth());
        vo.setTotalReceivableAmount(billMaster.getTotalReceivableAmount());
        vo.setSalaryAmount(billMaster.getSalaryAmount());
        vo.setManagementFeeAmount(billMaster.getManagementFeeAmount());
        vo.setOtherFeeAmount(billMaster.getOtherFeeAmount());
        vo.setTotalInvoiceAmount(billMaster.getTotalInvoiceAmount());
        vo.setInvoicedAmount(billMaster.getInvoicedAmount());
        vo.setReceivedAmount(billMaster.getReceivedAmount());
        vo.setBillStatus(billMaster.getBillStatus());
        vo.setBillStatusDesc(billMaster.getBillStatus().getDescription());
        vo.setConfirmTime(billMaster.getConfirmTime());
        vo.setConfirmUserId(billMaster.getConfirmUserId());
        vo.setRemark(billMaster.getRemark());
        vo.setCreateTime(billMaster.getCreateTime());
        vo.setModifyTime(billMaster.getModifyTime());
        return vo;
    }

    private BillCategoryVO convertToBillCategoryVO(BillCategoryEntity category) {
        BillCategoryVO vo = new BillCategoryVO();
        vo.setId(category.getId());
        vo.setBillMasterId(category.getBillMasterId());
        vo.setFeeType(category.getFeeType());
        vo.setFeeTypeDesc(category.getFeeType().getDescription());
        vo.setTotalAmount(category.getTotalAmount());
        vo.setDetailCount(category.getDetailCount());
        vo.setPersonCount(category.getPersonCount());
        vo.setCalculationRule(category.getCalculationRule());
        vo.setBillMonth(category.getBillMonth());
        vo.setRemark(category.getRemark());
        return vo;
    }

    // ==================== 权限校验辅助方法 ====================
    /**
     * 校验账单权限（带数据权限范围）
     */
    public void validateBillPermissionWithDataScope(Long supplierId, Long billId, Map<OwnerType, Set<Long>> dataScopes, boolean isAdmin) {

        if(isAdmin){
            return;
        }

        BillMasterEntity bill = billManager.getBillWithPermissionCheck(supplierId, billId);
        
        // 校验合同权限
        Set<Long> contractScopes = dataScopes.get(OwnerType.CONTRACT);
        if (contractScopes != null && !contractScopes.isEmpty() && !contractScopes.contains(bill.getContractId())) {
            throw new SecurityException("无权限操作该账单：合同权限不足");
        }
        //
        //// 校验客户权限
        //Set<Long> customerScopes = dataScopes.get(OwnerType.CUSTOMER);
        //if (customerScopes != null && !customerScopes.isEmpty() && !customerScopes.contains(bill.getCustomerId())) {
        //    throw new SecurityException("无权限操作该账单：客户权限不足");
        //}
        //
        //// 校验作业主体权限
        //Set<Long> corporationScopes = dataScopes.get(OwnerType.CORPORATION);
        //if (corporationScopes != null && !corporationScopes.isEmpty() && !corporationScopes.contains(bill.getSupplierCorporationId())) {
        //    throw new SecurityException("无权限操作该账单：作业主体权限不足");
        //}
    }

    // ==================== 其他费用导入功能 ====================

    /**
     * 下载其他费用导入模板
     */
    public void downloadOtherFeeImportTemplate(Long supplierId, HttpServletResponse response) {
        try {
            // 获取账单信息用于模板说明
            // 创建Excel模板
            ExcelResult excelResult = Excels.createWriteResult(BillOtherFeeImportRow.class);
            
            // 添加示例数据
            BillOtherFeeImportRow example1 = new BillOtherFeeImportRow();
            example1.setFeeName("交通费");
            example1.setFeeAmount(new java.math.BigDecimal("150.00"));
            // 账单月份中的某一天
            example1.setOccurDate(LocalDate.now());
            example1.setLaborName("张三");
            example1.setIdCard("110101199001011234");
            example1.setFeePurpose("出差交通费用");
            
            BillOtherFeeImportRow example2 = new BillOtherFeeImportRow();
            example2.setFeeName("餐费");
            example2.setFeeAmount(new java.math.BigDecimal("80.50"));
            example2.setOccurDate(LocalDate.now());
            example2.setLaborName("李四");
            example2.setIdCard("110101199002021234");
            example2.setFeePurpose("加班餐费补贴");
            
            excelResult.addRow(example1);
            excelResult.addRow(example2);
            
            // 生成并下载Excel文件
            final ExcelWriter excelWriter = Excels.writer(excelResult)
                    .sheetName(String.format("其他费用导入模板_%s", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月"))))
                    .build();



            String fileName = String.format("其他费用导入模板_%s.xlsx",LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月")));
            
            Excels.download(excelWriter.writeToWorkbook(), fileName, response);
            
            log.info("下载其他费用导入模板成功, supplierId: {}, fileName: {}",
                    supplierId, fileName);
                    
        } catch (Exception e) {
            log.error("下载其他费用导入模板失败, supplierId: {}", supplierId, e);
            throw new BusinessException("下载模板失败: " + e.getMessage());
        }
    }

    /**
     * 预览导入其他费用数据
     */
    @Transactional(readOnly = true)
    public BillOtherFeeImportPreviewResult previewOtherFeeImport(Long supplierId, Long billId, MultipartFile file) {
        log.info("预览导入其他费用数据, supplierId: {}, billId: {}, fileName: {}", 
                supplierId, billId, file.getOriginalFilename());
        
        try {
            // 1. 验证账单权限和状态
            BillMasterEntity bill = validateBillForImport(supplierId, billId);
            
            // 2. 解析Excel文件
            List<BillOtherFeeImportRow> importRows = parseExcelFile(file);
            
            // 3. 数据验证
            List<ImportValidationError> errors = importValidator.validateImportData(bill, importRows);
            
            // 4. 构建预览结果
            BillOtherFeeImportPreviewResult result = buildPreviewResult(bill, importRows, errors);
            
            log.info("预览导入其他费用数据完成, supplierId: {}, billId: {}, totalRows: {}, validRows: {}, errorRows: {}", 
                    supplierId, billId, result.getTotalRows(), result.getValidRows(), result.getErrorRows());
            
            return result;
            
        } catch (Exception e) {
            log.error("预览导入其他费用数据失败, supplierId: {}, billId: {}", supplierId, billId, e);
            throw new BusinessException("预览导入数据失败: " + e.getMessage());
        }
    }

    /**
     * 确认导入其他费用数据
     */
    public BillOtherFeeImportResult confirmOtherFeeImport(Long supplierId, Long billId, 
                                                         BillOtherFeeImportConfirmRequest request) {
        log.info("确认导入其他费用数据, supplierId: {}, billId: {}, importCount: {}", 
                supplierId, billId, request.getImportData().size());
        
        try {
            // 1. 验证账单权限和状态
            BillMasterEntity bill = validateBillForImport(supplierId, billId);
            
            // 2. 再次验证导入数据
            List<ImportValidationError> errors = importValidator.validateImportData(bill, request.getImportData());
            if (!errors.isEmpty()) {
                throw new BusinessException("导入数据验证失败，请检查数据格式");
            }
            
            return billManager.createImportOtherFee(supplierId, bill, request);
            
        } catch (Exception e) {
            log.error("确认导入其他费用数据失败, supplierId: {}, billId: {}", supplierId, billId, e);
            throw new BusinessException("导入数据失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证账单是否允许导入
     */
    private BillMasterEntity validateBillForImport(Long supplierId, Long billId) {
        BillMasterEntity bill = billManager.getBillWithPermissionCheck(supplierId, billId);
        
        // 只有特定状态的账单才允许导入其他费用
        if (bill.getBillStatus() == BillMasterStatus.CONFIRMED) {
            throw new BusinessException("当前账单状态不允许导入其他费用");
        }
        
        return bill;
    }

    /**
     * 解析Excel文件
     */
    private List<BillOtherFeeImportRow> parseExcelFile(MultipartFile file) {
        try {
            ExcelReader reader = Excels.reader(BillOtherFeeImportRow.class)
                .needVerify(true)
                .maxDataRows(1000) // 最多1000行
                .build();
            
            ExcelResult result = reader.read(file.getInputStream());
            return result.getResults();
            
        } catch (Exception e) {
            throw new BusinessException("解析Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 构建预览结果
     */
    private BillOtherFeeImportPreviewResult buildPreviewResult(BillMasterEntity bill, 
                                                              List<BillOtherFeeImportRow> importRows,
                                                              List<ImportValidationError> errors) {
        BillOtherFeeImportPreviewResult result = new BillOtherFeeImportPreviewResult();
        
        result.setSuccess(errors.isEmpty());
        result.setTotalRows(importRows.size());
        result.setErrorRows(errors.size());
        result.setValidRows(importRows.size() - errors.size());
        result.setErrors(errors);
        
        // 计算总金额和人数（仅统计有效数据）
        java.math.BigDecimal totalAmount = java.math.BigDecimal.ZERO;
        java.util.Set<String> personSet = new java.util.HashSet<>();
        
        for (BillOtherFeeImportRow row : importRows) {
            if (row.getFeeAmount() != null && !hasRowError(row.getRowNum(), errors)) {
                totalAmount = totalAmount.add(row.getFeeAmount());
            }
            if (row.getIdCard() != null && !hasRowError(row.getRowNum(), errors)) {
                personSet.add(row.getIdCard());
            }
        }
        
        result.setTotalAmount(totalAmount);
        result.setPersonCount(personSet.size());
        

        result.setPreviewData(importRows);
        
        // 设置账单信息
        result.setBillInfo(convertToBillMasterVO(bill));
        
        return result;
    }

    /**
     * 检查指定行是否有错误
     */
    private boolean hasRowError(int rowNum, List<ImportValidationError> errors) {
        return errors.stream().anyMatch(error -> error.getRowNum() == rowNum);
    }



}