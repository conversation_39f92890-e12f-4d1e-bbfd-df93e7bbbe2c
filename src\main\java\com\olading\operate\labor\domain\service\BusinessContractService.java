package com.olading.operate.labor.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.service.InfoSubmissionEnterpriseService;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class BusinessContractService {

    private final BusinessContractManager businessContractManager;
    private final AuthorityManager authorityManager;
    private final InfoSubmissionEnterpriseService infoSubmissionEnterpriseService;


    public void addContract(TenantInfo tenantInfo, ContractVo contractVo) {
        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(contractVo.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }
        BusinessContractEntity businessContractEntity = businessContractManager.addContract(tenantInfo,contractVo);
        if(CollectionUtil.isNotEmpty(contractVo.getRoleIds())){
            List<RoleData> list = contractVo.getRoleIds().stream().map(id -> {
                RoleData roleData = new RoleData();
                roleData.setId(id);
                return roleData;
            }).toList();
            list.forEach(o-> authorityManager.addRoleDataScopes(tenantInfo, o.getId(),new OwnedByFragment(OwnerType.CONTRACT, businessContractEntity.getId())));
        }

        // 创建企业信息报送记录
        try {
            infoSubmissionEnterpriseService.insertInfoSubmissionEnterprise(contractVo.getCustomerId());
        } catch (Exception e) {
            log.error("创建企业信息报送记录失败，customerId: {}", contractVo.getCustomerId(), e);
        }
    }

    public void updateContract(TenantInfo tenantInfo, ContractVo contractVo) {
        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(contractVo.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }
        businessContractManager.updateContract(contractVo);

        if(contractVo.getRoleIds() != null){
            List<RoleData> list = null;
            if(CollectionUtil.isNotEmpty(contractVo.getRoleIds())){
                list = contractVo.getRoleIds().stream().map(id -> {
                    RoleData roleData = new RoleData();
                    roleData.setId(id);
                    return roleData;
                }).toList();
            }
            authorityManager.editDataScope(tenantInfo, new OwnedByFragment(OwnerType.CONTRACT,  contractVo.getId()), list);
        }

    }

    public ContractVo queryContract(Long contractId) {
        ContractVo contractVo = businessContractManager.queryContract(contractId);
        // 查询客户关联的角色信息
        List<RoleData> roleDataList = authorityManager.getRoleByDataScope(OwnerType.CONTRACT, contractId);
        if (CollectionUtil.isNotEmpty(roleDataList)) {
            List<Long> roleIds = roleDataList.stream()
                    .map(RoleData::getId)
                    .collect(Collectors.toList());
            contractVo.setRoleIds(roleIds);
        }
        return contractVo;
    }

    public List<ContractVo> queryContractBySupplier(Long supplierId) {
        return businessContractManager.queryContractBySupplier(supplierId);
    }

    public void terminateContract(TenantInfo tenantInfo, Long contractId, String stopReason, Long updaterId) {
        businessContractManager.terminateContract(contractId, stopReason, updaterId);
    }
}
