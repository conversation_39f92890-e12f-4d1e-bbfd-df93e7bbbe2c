package com.olading.operate.labor.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class NullIfEmptyInListDeserializer extends JsonDeserializer<List<String>> implements ContextualDeserializer {

    private JavaType contentType;

    public NullIfEmptyInListDeserializer() {}

    public NullIfEmptyInListDeserializer(JavaType contentType) {
        this.contentType = contentType;
    }


    @Override
    public List<String> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        if (node == null || !node.isArray()) {
            return null;
        }

        List<String> result = new ArrayList<>();
        for (JsonNode element : node) {
            if (element.isNull()) {
                result.add(null);
            } else {
                String text = element.asText();
                result.add(!StringUtils.hasText(text) ? null : text);
            }
        }

        return result;
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) throws JsonMappingException {
        JavaType type = property.getType();
        // 防止错误用于 List<Object> 或 List<InvoiceItemRequest>
        if (!type.hasGenericTypes() || !type.getContentType().getRawClass().equals(String.class)) {
            throw new JsonMappingException(ctxt.getParser(), "NullIfEmptyInListDeserializer only supports List<String>");
        }
        return this;
    }
}