package com.olading.operate.labor.domain.query;

import cn.hutool.core.collection.CollectionUtil;
import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.salary.QSalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.salary.vo.SalaryVO;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Set;

public class SalaryQuery implements EntityQuery<QueryFilter<SalaryQuery.Filters>, SalaryVO> {

    private final QSalaryStatementEntity t = QSalaryStatementEntity.salaryStatementEntity;

    private final QBusinessContractEntity t2 = QBusinessContractEntity.businessContractEntity;

    private final QCustomerEntity t3 = QCustomerEntity.customerEntity;

    private final QSupplierCorporationEntity t4 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<SalaryQuery.Filters> filters) {
        BooleanBuilder builder = new BooleanBuilder();

        SalaryQuery.Filters f = filters.getFilters();

        if (f.getId() != null) {
            builder.and(t.id.eq(f.getId()));
        }

        if (f.getContractId() != null) {
            builder.and(t.contractId.eq(f.getContractId()));
        }
        if (StringUtils.isNotBlank(f.getContractName())) {
            builder.and(t2.name.like(JpaUtils.fullLike(f.getContractName())));
        }
        if (CollectionUtil.isNotEmpty(f.getCustomerId())) {
            builder.and(t.customerId.in(f.getCustomerId()));
        }
        if (StringUtils.isNotBlank(f.getCustomerName())) {
            builder.and(t3.name.like(JpaUtils.fullLike(f.getCustomerName())));
        }
        if (f.getSupplierCorporationId() != null) {
            builder.and(t.supplierCorporationId.eq(f.getSupplierCorporationId()));
        }
        if (StringUtils.isNotBlank(f.getSupplierCorporationName())) {
            builder.and(t4.name.like(JpaUtils.fullLike(f.getSupplierCorporationName())));
        }
        if (StringUtils.isNotBlank(f.getStatus())) {
            builder.and(t.status.eq(SalaryStatementStatus.valueOf(f.getStatus())));
        }
        if (!filters.isWithDeleted()) {
            builder.and(t.deleted.eq(false));
        }
        if (CollectionUtil.isNotEmpty(filters.getFilters().getContractIds())){
            builder.and(t.contractId.in(filters.getFilters().getContractIds()));
        }

        query.select(t,t2,t3,t4)
                .from(t)
                .leftJoin(t2).on(t.contractId.eq(t2.id))
                .leftJoin(t3).on(t.customerId.eq(t3.id))
                .leftJoin(t4).on(t.supplierCorporationId.eq(t4.id))
                .where(builder);
    }

    @Override
    public SalaryVO transform(Object v) {
        Tuple tuple = (Tuple) v;
        SalaryStatementEntity entity = tuple.get(t);
        BusinessContractEntity businessContractEntity = tuple.get(t2);
        CustomerEntity customerEntity = tuple.get(t3);
        SupplierCorporationEntity supplierCorporationEntity = tuple.get(t4);
        SalaryVO vo = new SalaryVO();
        assert entity != null;
        BeanUtils.copyProperties(entity, vo);
        if(businessContractEntity != null){
            vo.setContractName(businessContractEntity.getName());
        }
        if(customerEntity != null){
            vo.setCustomerName(customerEntity.getName());
        }
        if(supplierCorporationEntity != null){
            vo.setSupplierCorporationName(supplierCorporationEntity.getName());
        }
        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t.id;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private Long contractId;
        private String contractName;
        private List<Long> customerId;
        private String customerName;
        private Long supplierCorporationId;
        private String supplierCorporationName;
        private String status;
        private Set<Long> contractIds;
    }
}
