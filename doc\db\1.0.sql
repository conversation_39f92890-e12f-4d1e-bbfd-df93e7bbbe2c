drop database olading_labor;
CREATE DATABASE operate_tax;
use olading_labor;

drop table if exists t_user;
create table t_user
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6) null comment '创建时间',
    deleted      bit         null comment '是否已删除',
    deleted_time datetime(6) null comment '删除时间',
    modify_time  datetime(6) null comment '更新时间',
    tenant_id    varchar(20) null comment '租户编号',
    version      int         not null comment '版本',
    name         varchar(20) null comment '用户名',
    account_no   varchar(20)  null comment '账号',
    password     varchar(50) null comment '密码',
    cellphone    varchar(11) null comment '手机号'
)
    comment '用户';


create unique index idx_tenant_cell
    on t_user (tenant_id, cellphone);

create unique index t_user_tenant_acc
    on t_user (tenant_id, account_no);




drop table if exists t_person_info;
create table t_person_info
(
    id            bigint auto_increment
        primary key,
    create_time   datetime(6) null comment '创建时间',
    deleted       bit         null comment '是否已删除',
    deleted_time  datetime(6) null comment '删除时间',
    modify_time   datetime(6) null comment '更新时间',
    tenant_id     varchar(20) null comment '租户编号',
    version       int         not null comment '版本',
    cellphone     varchar(11) null comment '手机号',
    name          varchar(20) null comment '人员姓名',
    id_card        varchar(20) null comment '身份证号',
    owner_id bigint      null comment '实体ID',
    owner_type    varchar(20) null comment '实体类型-SUPPLIER,CUSTOMER,USER,BOSS,TRANS_RECEIPT',
    constraint i_person_info_1
        unique (owner_id, owner_type)
)
    comment '人员信息';


create table t_role
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6)  null comment '创建时间',
    deleted      bit          null comment '是否已删除',
    deleted_time datetime(6)  null comment '删除时间',
    modify_time  datetime(6)  null comment '更新时间',
    tenant_id    varchar(20)  null comment '租户编号',
    version      int          not null comment '版本',
    name         varchar(20)  null comment '角色名',
    authorities  longtext     null comment '角色拥有的权限',
    remark       varchar(200) null comment '备注',
    code         varchar(10)  null comment '角色编码',
    disabled     bit  default false not null comment '是否禁用'
);

drop table if exists t_role_data_scope;
create table t_role_data_scope
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6)  null comment '创建时间',
    deleted      bit          null comment '是否已删除',
    deleted_time datetime(6)  null comment '删除时间',
    modify_time  datetime(6)  null comment '更新时间',
    tenant_id    varchar(20)  null comment '租户编号',
    version      int          not null comment '版本',
    name         varchar(20)  null comment '角色名',
    code         varchar(10)  null comment '角色编码',
    role_id         bigint   not null comment '角色id',
    data_type       varchar(32) null comment '权限类型:CORPORATION-作业主体,CUSTOMER-客户,CONTRACT-合同',
    data_id       bigint null comment '权限类型对应数据ID'
);

drop table if exists t_supplier_member;
create table t_supplier_member
(
    id             bigint auto_increment
        primary key,
    create_time    datetime(6) null comment '创建时间',
    deleted        bit         null comment '是否已删除',
    deleted_time   datetime(6) null comment '删除时间',
    modify_time    datetime(6) null comment '更新时间',
    tenant_id      varchar(20) null comment '租户编号',
    version        int         not null comment '版本',
    supplier_id    bigint      not null comment '服务运营方编号',
    last_used_time datetime(6) null comment '上一次使用的时间',
    disabled       bit         not null default false comment '是否禁用',
    user_id        bigint      null comment '用户ID',
    owner_type      varchar(20)      null comment '所属类型:1-平台用户,2-客户用户',
    owner_Id        bigint       not null comment '平台id,客户id',
    name           varchar(20) null comment '姓名',
    cellphone      varchar(11) not null comment '手机号'
)  comment '平台用户表';



drop table if exists t_role_member;
create table t_role_member
(
    id              bigint auto_increment
        primary key,
    create_time     datetime(6) null comment '创建时间',
    deleted         bit         null comment '是否已删除',
    deleted_time    datetime(6) null comment '删除时间',
    modify_time     datetime(6) null comment '更新时间',
    tenant_id       varchar(20) null comment '租户编号',
    version         int         not null comment '版本',
    role_id         bigint      not null comment '角色ID',
    subject_id         bigint      null comment '用户ID'
);


drop table if exists olading_labor.t_enterprise_info;
create table olading_labor.t_enterprise_info
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)   null comment '创建时间',
    deleted                 bit           null comment '是否已删除',
    deleted_time            datetime(6)   null comment '删除时间',
    modify_time             datetime(6)   null comment '更新时间',
    tenant_id               varchar(20)   null comment '租户编号',
    version                 int           not null comment '版本',
    contact_phone           varchar(100)  null comment '联系电话',
    contacts                varchar(100)  null comment '联系人',
    name                    varchar(100)  null comment '名称',
    owner_id                bigint        null comment '实体ID',
    owner_type              varchar(20)   null comment '实体类型 SUPPLIER,CORPORATION,CUSTOMER',
    attachments             longtext      null comment '附件文件ID列表',
    remark                  varchar(1024) null comment '备注',
    business_license_image  varchar(64)   null comment '营业执照图片id',
    social_credit_code      varchar(64)   null comment '统一社会信用代码',
    representative_name     varchar(64)   null comment '法定代表人姓名',
    certificate_type        tinyint       null comment '法定代表人证件类型',
    certificate_no          varchar(64)   null comment '法定代表人证件号',
    certificate_front_image varchar(64)   null comment '法定代表人证件正面照',
    certificate_back_image  varchar(64)   null comment '法定代表人证件背面照',
    registered_address  varchar(64)       null comment '公司注册地址',
    constraint i_enterprise_info_1
        unique (owner_id, owner_type)
)
    comment '企业信息';


drop table if exists t_supplier;
create table t_supplier
(
    id                            bigint auto_increment
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                       int          not null comment '版本',
    supplier_no                   varchar(100) null comment '服务商编号',
    name                          varchar(20)  null comment '名称',
    enterprise_info_id            bigint       null comment '企业信息ID',
    business_create_time          datetime(6)  null comment '业务创建时间',
    signature_code                varchar(50)  null comment '短信签名',
    disabled                      bit          null comment '是否禁用(0:否,1:禁用)',
    admin_user_id                 bigint       null comment '管理员用户ID'
) comment '灵工平台服务商信息';


create table t_file
(
    id            varchar(32)  not null
        primary key,
    create_time   datetime(6)  null comment '创建时间',
    deleted       bit          null comment '是否已删除',
    deleted_time  datetime(6)  null comment '删除时间',
    modify_time   datetime(6)  null comment '更新时间',
    tenant_id     varchar(20)  null comment '租户编号',
    version       int          not null comment '版本',
    expiry_time   datetime(6)  null comment '文件过期时间',
    name          varchar(200) null comment '文件名',
    owner_id       bigint       null comment '实体ID',
    owner_type    varchar(20)  null comment '实体类型',
    storage_id    varchar(100) null
)
    comment '文件表';

create index i_file_1
    on t_file (owner_id, owner_type);


create table t_task
(
    id            bigint auto_increment comment '任务ID'
        primary key,
    task_type     varchar(25)   null comment '任务类型',
    task_status   varchar(25)   null comment '任务状态',
    task_param    varchar(1024) null comment '任务参数',
    task_result   varchar(255)  null comment '任务执行结果，可以是文本或JSON格式',
    finish_time   datetime(6)   null comment '任务结束时间',
    file_name     varchar(255)  null comment '文件名称',
    attachments   longtext      null comment '附件文件ID列表',
    owner_type    varchar(25)   null comment '实体类型',
    owner_id bigint        null comment '实体id',
    create_time   datetime(6)   null comment '任务创建时间',
    deleted       bit           null comment '是否已删除',
    deleted_time  datetime(6)   null comment '删除时间',
    modify_time   datetime(6)   null comment '更新时间',
    tenant_id     varchar(20)   null comment '租户编号',
    version       int           null comment '版本'
)
    comment '异步任务表，用于存储任务的核心信息';





drop table if exists t_supplier_domain_config;
create table t_supplier_domain_config
(
    id             bigint auto_increment
        primary key,
    create_time    datetime(6) null comment '创建时间',
    deleted        bit         null comment '是否已删除',
    deleted_time   datetime(6) null comment '删除时间',
    modify_time    datetime(6) null comment '更新时间',
    tenant_id      varchar(20) null comment '租户编号',
    version        int         not null comment '版本',
    supplier_id    bigint      not null comment '服务运营方编号',
    domain_name         varchar(60)   null comment '二级域名',
    slogan              varchar(20)   null comment 'slogan',
    logo_url            varchar(255)  null comment 'logo地址',
    brand_name          varchar(255)   null comment '品牌名称',
    h5_domain_name         varchar(60)   null comment 'h5域名',
    h5_logo_url         varchar(255)   null comment 'h5_logo地址',
    h5_service_agreement text  null comment 'h5服务协议文件及文件地址,以json方式存储'
)
    comment '品牌信息配置表';


drop table if exists t_csupplier_pay_channel;
create table t_supplier_pay_channel
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    pay_channel     varchar(64)   not null comment '通道编码',
    pay_channel_id     bigint   not null comment '通道配置id'
)
    comment '平台可用支付通道配置';


drop table if exists t_pay_channel_config;
create table t_pay_channel_config
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    pay_channel     varchar(64)   not null comment '通道编码',
    channel_name     varchar(64)   not null comment '通道名称',
    pay_channel_config     longtext   not null comment '通道配置项(json)'
)
    comment '支付通道配置项';



drop table if exists t_supplier_sms_template;
create table t_supplier_sms_template
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    business_type       varchar(64) null  comment '短信业务类型',
    template_code       varchar(256) null comment '短信模板编码'
)
    comment '平台短信配置';

create unique index udx_supplier_bus
    on t_supplier_sms_template (supplier_id, business_type);


create table t_system_feerate_record
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    FEE_TYPE            varchar(30)    null comment '收费类型',
    FEE_MODEL           varchar(30)    null comment '计费模式',
    EFFECT_START_TIME   datetime(6)    null comment '生效开始时间',
    EFFECT_END_TIME     datetime(6)    null comment '生效结束时间',
    MONTH_RATE          decimal(19, 4) null comment '月度基础费率',
    LAST_MONTH_RATE     decimal(19, 4) null comment '上月月度基础费率',
    START_TIME          datetime(6)    null comment '收费开始时间',
    END_TIME            datetime(6)    null comment '收费结束时间'
)
    comment '平台服务费配置';




create table t_supplier_labor
(
    id             bigint auto_increment
        primary key,
    create_time    datetime(6) null comment '创建时间',
    deleted        bit         null comment '是否已删除',
    deleted_time   datetime(6) null comment '删除时间',
    modify_time    datetime(6) null comment '更新时间',
    tenant_id      varchar(20) null comment '租户编号',
    version        int         not null comment '版本',
    supplier_id    bigint      not null comment '服务运营方编号',
    name           varchar(20) null comment '姓名',
    id_card           varchar(20) null comment '身份证号',
    id_card           varchar(20) null comment '身份证号',
    cellphone      varchar(11) not null comment '手机号,平台下唯一',
    bank_card     varchar(24) null comment '银行卡号'

)  comment '平台劳务人员表';


drop table if exists t_supplier_corporation;
create table t_supplier_corporation
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    tax_uuid             varchar(64)        null comment '税局给的平台UUID',
    enterprise_info_id      bigint        null comment '企业信息ID',
    name                    varchar(64)   not null comment '公司名称',
    social_credit_code      varchar(64)   not null comment '统一社会信用代码',
    bank_name               varchar(64)   null comment '开户行',
    bank_account            varchar(64)   null comment '开户账号',
    company_tel             varchar(64)   null comment '企业电话',
    disabled                      bit          null comment '是否禁用'
)
    comment '作业主体信息';

drop table if exists t_corporation_config;
create table t_corporation_config
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    supplier_corporation_id      bigint   not null comment '作业主体ID',
    min_age_limit     int(1)   null comment '最小年龄限制',
    max_age_limit     int(1)   null comment '最大年龄限制',
    invoice_category          text null comment '发票类目配置(json)',
    vat_start          decimal(19,2) null comment '增值税起征点(万元)',
    vat_rate          decimal(19,2) null comment '增值税税率(%)',
    surtax_data       text null comment '附加税配置(json)'
) comment '作业主体配置';

drop table if exists t_corporation_pay_channel;
create table t_corporation_pay_channel
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    supplier_corporation_id      bigint   not null comment '作业主体ID',
    pay_channel     varchar(64)   not null comment '通道编码',
    is_default     bit(1)   not null default false comment '默认通道',
    is_open     bit(1)   not null default true comment '是否启用',
    channel_config          text null comment '通道配置信息'
)

    comment '作业主体通道配置信息';

create table t_corporation_protocol_template
(

    id                      bigint auto_increment comment 'id'
        primary key,
    create_time                   datetime(6)  null comment '创建时间',
    deleted                       bit          null comment '是否已删除',
    deleted_time                  datetime(6)  null comment '删除时间',
    modify_time                   datetime(6)  null comment '更新时间',
    tenant_id                     varchar(20)  null comment '租户编号',
    version                   int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    supplier_corporation_id      bigint   not null comment '作业主体ID',
    SIGN_REQUEST_TYPE   varchar(30) not null comment '协议类型',
    AGENT_TEMPLATE      varchar(30) null comment '协议模版id',
    STATUS              varchar(10) not null comment '协议可用状态'
)
    comment '作业主体协议模版配置信息';

create table t_corporation_protocol_temp_filed
(
    id                      bigint auto_increment
        primary key,
    tenant_id               varchar(20)  not null comment '终端id',
    version                 int          null,
    supplier_id             int          null comment '平台id',
    supplier_corporation_id bigint       null comment '作业主体id',
    template_id             bigint       not null comment '协议模板id',
    template_step_id        bigint       not null comment '模板步骤id',
    template_step_name      varchar(32)  null comment '模板步骤名',
    field_name              varchar(32)  null comment '字段名',
    relation_code           varchar(32)  null comment '关联项',
    relation_name           varchar(128) null comment '关联项名称',
    relation_group          varchar(32)  null comment '关联项所属分组',
    create_time             datetime     null comment '创建时间',
    modify_time             datetime     null comment '更新时间',
    operator                varchar(32)  null comment '域类型 SEAL-企业签章 SIGN-个人签章 DATE-日期 FIELD-填充域'
)
    comment '协议模板域信息';


drop table if exists olading_labor.t_customer;
create table olading_labor.t_customer
(
    id                 bigint auto_increment
        primary key,
    create_time        datetime(6)  null comment '创建时间',
    deleted            bit          null comment '是否已删除',
    deleted_time       datetime(6)  null comment '删除时间',
    modify_time        datetime(6)  null comment '更新时间',
    tenant_id          varchar(20)  null comment '租户编号',
    version            int          null comment '版本',
    supplier_id        bigint       null comment '灵工平台id',
    name               varchar(200) not null comment '客户名称',
    enterprise_info_id bigint       null comment '企业信息ID',
    sn                 varchar(64)  null comment '编号',
    short_name         varchar(64)  null comment '简称',
    status             varchar(20)  null comment '状态',
    region_id          varchar(64)  null comment '地区id',
    address            varchar(160) null comment '详细地址',
    industry           varchar(20)  null comment '行业',
    type               varchar(20)  null comment '性质',
    size               varchar(20)  null comment '规模',
    source             varchar(64)  null comment '来源',
    sales_name         varchar(20)  null comment '销售负责人姓名',
    service_mobile     varchar(20)  null comment '销售负责人电信',
    contact_name       varchar(64)  null comment '客户联系人姓名',
    contact_mobile     varchar(64)  null comment '客户联系人电话',
    remark             varchar(200) null comment '备注',
    disabled           bit          null comment '是否禁用',
    user_id            bigint       null comment '用户ID'
)
    comment '客户信息表';


drop table if exists olading_labor.t_business_contract;
create table olading_labor.t_business_contract
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)   null comment '创建时间',
    deleted                 bit           null comment '是否已删除',
    deleted_time            datetime(6)   null comment '删除时间',
    modify_time             datetime(6)   null comment '更新时间',
    tenant_id               varchar(20)   null comment '租户编号',
    version                 int           not null comment '版本',
    supplier_id             bigint        null comment '灵工平台id',
    customer_id             bigint        not null comment '客户id',
    supplier_corporation_id bigint        not null comment '作业主体id',
    name                    varchar(150)  not null comment '合同名称',
    status                  varchar(20)   not null comment '状态',
    sn                      varchar(64)   not null comment '编号',
    time_fixed              tinyint(1)    not null comment '合同期限是否固定',
    start_date              date          null comment '开始日期',
    end_date                date          null comment '结束日期',
    stopped                 tinyint(1)    null comment '是否已提前中止:0,1',
    stop_time               datetime      null comment '提前中止时间',
    stop_reason             varchar(500)  null comment '提前中止原因',
    business_type           varchar(16)   null comment '业务类型',
    remark                  varchar(500)  null comment '备注',
    file_ids                varchar(5000) null comment '附件id,逗号分隔',
    pre_contract_id         bigint        null comment '原合同id',
    rear_contract_id        bigint        null comment '续签合同id',
    creator_id              bigint        null comment '创建者id',
    updater_id              bigint        null comment '更新者id'

)
    comment '客户服务合同';

drop table if exists olading_labor.t_business_contract_config;
create table olading_labor.t_business_contract_config
(
    id                       bigint auto_increment
        primary key,
    create_time              datetime(6)    null comment '创建时间',
    deleted                  bit            null comment '是否已删除',
    deleted_time             datetime(6)    null comment '删除时间',
    modify_time              datetime(6)    null comment '更新时间',
    tenant_id                varchar(20)    null comment '租户编号',
    version                  int            not null comment '版本',
    customer_id              bigint         not null comment '客户id',
    supplier_corporation_id  bigint         not null comment '作业主体id',
    contract_id              bigint         not null comment '合同名称',
    invoice_title            varchar(64)    null comment '抬头',
    invoice_tax_no           varchar(64)    null comment '纳税识别号',
    invoice_bank_name        varchar(64)    null comment '开户行',
    invoice_bank_account     varchar(64)    null comment '账号',
    invoice_register_address varchar(64)    null comment '注册地址',
    invoice_company_tel      varchar(64)    null comment '企业电话',
    invoice_remark           varchar(500)   null comment '发票备注',
    manage_calculation_rule  varchar(20)    null comment '计算规则',
    manage_amount            decimal(16, 2) null comment '金额',
    manage_rate              decimal(16, 2) null comment '费率'
)
    comment '服务合同配置';

create table t_business_contract_item
(
    id                 bigint auto_increment
        primary key,
    create_time        datetime(6)  null comment '创建时间',
    deleted            bit          null comment '是否已删除',
    deleted_time       datetime(6)  null comment '删除时间',
    modify_time        datetime(6)  null comment '更新时间',
    tenant_id          varchar(20)  null comment '租户编号',
    version            int          not null comment '版本',
    supplier_id        bigint        not null comment '灵工平台id',
    customer_id                 bigint        not null comment '客户id',
    supplier_corporation_id      bigint   not null comment '作业主体ID',
    contract_id     bigint       not null comment '合同id',
    service_code    varchar(16)  not null comment '服务项目code',
    settlement_type tinyint      not null comment '结算规则',
    fee_type        tinyint      not null comment '费用类型',
    include_type    tinyint      not null comment '是否计入开票总额',
    item_rule       text         null comment '服务项目规则，json'
)
    comment '客户合同服务项';

-- 账单管理模块 V2 数据库表结构
-- 三层设计：总账单表 -> 分类统计表 -> 明细表

-- 第一层：账单主表
DROP TABLE IF EXISTS t_bill_master;
CREATE TABLE t_bill_master (
                               id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                               bill_no VARCHAR(64) NOT NULL UNIQUE COMMENT '账单编号',
                               supplier_id BIGINT NOT NULL COMMENT '供应商ID',
                               customer_id BIGINT NOT NULL COMMENT '客户ID',
                               supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                               contract_id BIGINT NOT NULL COMMENT '客户合同ID',
                               bill_month DATE NOT NULL COMMENT '账单月份',
                               total_receivable_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '账单应收总费用',
                               salary_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '薪酬费用总额',
                               management_fee_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '管理费总额',
                               other_fee_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '其他费用总额',
                               total_invoice_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '开票总金额',
                               invoiced_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '已开票金额',
                               received_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '已收款金额',
                               bill_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '账单状态',
                               confirm_time DATETIME COMMENT '确认时间',
                               confirm_user_id BIGINT COMMENT '确认人ID',
                               remark VARCHAR(500) COMMENT '备注',

    -- 基础字段
                               tenant_id VARCHAR(20) COMMENT '租户编号',
                               create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                               modify_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                               deleted BIT DEFAULT 0 COMMENT '是否删除',
                               deleted_time DATETIME(6) COMMENT '删除时间',
                               version INT NOT NULL DEFAULT 0 COMMENT '版本号',

    -- 索引
                               INDEX idx_supplier_customer (supplier_id, customer_id),
                               INDEX idx_contract_month (contract_id, bill_month),
                               INDEX idx_bill_status (bill_status),
                               INDEX idx_create_time (create_time)
) COMMENT '账单主表';

-- 第二层：账单分类统计表
DROP TABLE IF EXISTS t_bill_category;
CREATE TABLE t_bill_category (
                                 id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                 bill_master_id BIGINT NOT NULL COMMENT '账单主表ID',
                                 fee_type VARCHAR(20) NOT NULL COMMENT '费用类型：SALARY-薪酬,MANAGEMENT_FEE-管理费,OTHER_FEE-其他费用',
                                 total_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '费用总额',
                                 detail_count INT NOT NULL DEFAULT 0 COMMENT '明细条数',
                                 person_count INT NOT NULL DEFAULT 0 COMMENT '涉及人数',
                                 calculation_rule VARCHAR(200) COMMENT '计算规则说明',
                                 bill_month DATE NOT NULL COMMENT '账单月份',
                                 remark VARCHAR(500) COMMENT '备注',

    -- 基础字段
                                 tenant_id VARCHAR(20) COMMENT '租户编号',
                                 create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                                 modify_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                                 deleted BIT DEFAULT 0 COMMENT '是否删除',
                                 deleted_time DATETIME(6) COMMENT '删除时间',
                                 version INT NOT NULL DEFAULT 0 COMMENT '版本号',

    -- 索引
                                 INDEX idx_bill_master (bill_master_id),
                                 INDEX idx_fee_type (fee_type),
                                 UNIQUE KEY uk_bill_fee_type (bill_master_id, fee_type)
) COMMENT '账单分类统计表';

-- 第三层：薪酬明细表
DROP TABLE IF EXISTS t_bill_salary_detail;
CREATE TABLE t_bill_salary_detail (
                                      id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                      bill_master_id BIGINT NOT NULL COMMENT '账单主表ID',
                                      bill_category_id BIGINT NOT NULL COMMENT '账单分类ID',
                                      salary_detail_id BIGINT NOT NULL COMMENT '薪酬明细ID',
                                      salary_batch_id BIGINT NOT NULL COMMENT '薪酬批次ID',
                                      labor_name VARCHAR(50) NOT NULL COMMENT '人员姓名',
                                      id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
                                      gross_salary DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '应发工资',
                                      net_salary DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '实发工资',
                                      income_tax DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '应缴个税',
                                      vat_tax DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '应缴增值税',
                                      additional_tax DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '应缴附加税',
                                      bill_month DATE NOT NULL COMMENT '账单月份',
                                      salary_period VARCHAR(20) COMMENT '工资所属期',
                                      remark VARCHAR(200) COMMENT '备注',

    -- 基础字段
                                      tenant_id VARCHAR(20) COMMENT '租户编号',
                                      create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                                      modify_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                                      deleted BIT DEFAULT 0 COMMENT '是否删除',
                                      deleted_time DATETIME(6) COMMENT '删除时间',
                                      version INT NOT NULL DEFAULT 0 COMMENT '版本号',

    -- 索引
                                      INDEX idx_bill_master (bill_master_id),
                                      INDEX idx_bill_category (bill_category_id),
                                      INDEX idx_salary_batch (salary_batch_id),
                                      INDEX idx_id_card (id_card),
                                      INDEX idx_bill_month (bill_month),
                                      UNIQUE KEY uk_salary_detail (bill_master_id, salary_detail_id)
) COMMENT '账单薪酬明细表';

-- 第三层：管理费明细表
DROP TABLE IF EXISTS t_bill_management_fee_detail;
CREATE TABLE t_bill_management_fee_detail (
                                              id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                              bill_master_id BIGINT NOT NULL COMMENT '账单主表ID',
                                              bill_category_id BIGINT NOT NULL COMMENT '账单分类ID',
                                              labor_name VARCHAR(50) NOT NULL COMMENT '人员姓名',
                                              id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
                                              fee_item VARCHAR(100) NOT NULL COMMENT '收费项目',
                                              management_fee_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '管理费金额',
                                              bill_month DATE NOT NULL COMMENT '账单月份',
                                              calculation_base DECIMAL(19,2) COMMENT '计算基数',
                                              calculation_rate DECIMAL(8,4) COMMENT '计算费率',
                                              calculation_rule VARCHAR(50) COMMENT '计算规则',
                                              remark VARCHAR(200) COMMENT '备注',

    -- 基础字段
                                              tenant_id VARCHAR(20) COMMENT '租户编号',
                                              create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                                              modify_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                                              deleted BIT DEFAULT 0 COMMENT '是否删除',
                                              deleted_time DATETIME(6) COMMENT '删除时间',
                                              version INT NOT NULL DEFAULT 0 COMMENT '版本号',

    -- 索引
                                              INDEX idx_bill_master (bill_master_id),
                                              INDEX idx_bill_category (bill_category_id),
                                              INDEX idx_id_card (id_card),
                                              INDEX idx_bill_month (bill_month),
                                              INDEX idx_fee_item (fee_item)
) COMMENT '账单管理费明细表';

-- 第三层：其他费用明细表
DROP TABLE IF EXISTS t_bill_other_fee_detail;
CREATE TABLE t_bill_other_fee_detail (
                                         id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                         bill_master_id BIGINT NOT NULL COMMENT '账单主表ID',
                                         bill_category_id BIGINT NOT NULL COMMENT '账单分类ID',
                                         labor_name VARCHAR(50) NOT NULL COMMENT '人员姓名',
                                         id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
                                         occur_date DATE COMMENT '产生时间',
                                         fee_name VARCHAR(100) NOT NULL COMMENT '费用名称',
                                         fee_amount DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '费用金额',
                                         fee_purpose VARCHAR(200) NOT NULL COMMENT '费用用途',
                                         bill_month DATE NOT NULL COMMENT '账单月份',
                                         fee_category VARCHAR(50) COMMENT '费用类别',
                                         fee_description VARCHAR(500) COMMENT '费用说明',
                                         import_batch_no VARCHAR(50) COMMENT '导入批次号',
                                         remark VARCHAR(200) COMMENT '备注',

    -- 基础字段
                                         tenant_id VARCHAR(20) COMMENT '租户编号',
                                         create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                                         modify_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                                         deleted BIT DEFAULT 0 COMMENT '是否删除',
                                         deleted_time DATETIME(6) COMMENT '删除时间',
                                         version INT NOT NULL DEFAULT 0 COMMENT '版本号',

    -- 索引
                                         INDEX idx_bill_master (bill_master_id),
                                         INDEX idx_bill_category (bill_category_id),
                                         INDEX idx_id_card (id_card),
                                         INDEX idx_bill_month (bill_month),
                                         INDEX idx_fee_name (fee_name),
                                         INDEX idx_import_batch (import_batch_no)
) COMMENT '账单其他费用明细表';


drop table  if exists t_invoice;
create table t_invoice
(
    id                 bigint auto_increment
        primary key,
    create_time        datetime(6)  null comment '创建时间',
    deleted            bit          null comment '是否已删除',
    deleted_time       datetime(6)  null comment '删除时间',
    modify_time        datetime(6)  null comment '更新时间',
    tenant_id          varchar(20)  null comment '租户编号',
    version            int          not null comment '版本',
    supplier_id        bigint        not null comment '灵工平台id',
    customer_id                 bigint        not null comment '客户id',
    supplier_corporation_id      bigint   not null comment '作业主体ID',
    contract_id     bigint       not null comment '合同id',
    sn                          varchar(64)    not null comment '申请编号',
    status                      varchar(64)        not null comment '状态',
    type                        varchar(64)        not null comment '发票类型-SPECIAL(专票)/GENERAL(普票)  ',
    fee                         decimal(12, 2) not null comment '开票金额',
    title                       varchar(64)    not null comment '抬头',
    tax_no                      varchar(64)    not null comment '纳税识别号',
    bank_name                   varchar(64)    not null comment '开户行',
    bank_account                varchar(64)    not null comment '银行账号',
    register_address            varchar(64)    not null comment '注册地址',
    company_tel                 varchar(64)    not null comment '企业电话',
    remark                      varchar(500)   null comment '发票备注',
    apply_remark                varchar(500)   null comment '申请备注',
    addressee_name              varchar(64)    not null comment '收件人姓名',
    addressee_mobile            varchar(64)    not null comment '收件人电话',
    addressee_address           varchar(64)    not null comment '收件人地址',
    addressee_email             varchar(64)    not null comment '收件人邮箱',
    invalid_reason              varchar(300)   null comment '作废原因',
    back_reason                 varchar(300)   null comment '退回原因',
    invoice_file        varchar(256)    null comment '发票文件'
)
    comment '开票';

drop table if exists t_invoice_item;
create table t_invoice_item
(
    id                 bigint auto_increment
        primary key,
    create_time        datetime(6)  null comment '创建时间',
    deleted            bit          null comment '是否已删除',
    deleted_time       datetime(6)  null comment '删除时间',
    modify_time        datetime(6)  null comment '更新时间',
    tenant_id          varchar(20)  null comment '租户编号',
    version            int          not null comment '版本',
    contract_id     bigint       not null comment '服务合同id',
    invoice_id     bigint          not null comment '开票id',
    bill_id        bigint          not null comment '账单id',
    invoice_category        varchar(64)          not null comment '发票类目',
    fee            decimal(12, 2)  not null comment '开票金额'
)
    comment '开票明细';


create table t_channel_remit_order
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    proxy_order_id          bigint         not null comment '工资代发订单id',
    pay_channel             varchar(64)    not null comment '通道编码',
    request_no              varchar(64)    not null comment '出款流水号',
    status                  varchar(20)    not null comment '状态',
    name                    varchar(20)    not null comment '姓名',
    id_card                 varchar(20)    not null comment '身份证号',
    cellphone               varchar(11)    not null comment '手机号',
    bank_card               varchar(24)    not null comment '银行卡号',
    bank_code               varchar(20)    null comment '出款银行编码',
    bank_name               varchar(20)    null comment '出款银行名称',
    amount                  decimal(38, 2) not null comment '付款金额',
    error_code              varchar(64)    null comment '错误码',
    error_reason            varchar(100)   null comment '错误原因',
    finish_time             datetime(6)    null comment '完成时间',
    voucher                 varchar(50)    null comment '凭证文件id',
    remark                  varchar(200)   null comment '业务备注'
)
    comment '通道出款订单';

create table olading_labor.t_proxy_batch
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    customer_id             bigint         not null comment '客户id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    contract_id             bigint         not null comment '合同id',
    salary_statement_id     bigint         not null comment '工资批次id',
    total_amount            decimal(19, 2) null comment '总金额',
    pay_channel             varchar(64)    not null comment '通道编码',
    count                   bigint         null comment '总笔数',
    confirm_time            datetime(6)    null comment '确认出款时间',
    complete_time           datetime(6)    null comment '完成时间',
    last_error_info         varchar(256)   null comment '错误信息',
    batch_status            varchar(20)    not null comment '批次状态 CHECK/PROCESSING/COMPLETE/DELETED',
    remark                  varchar(256)   null comment '备注'
)
    comment '工资代发批次';

drop table if exists t_proxy_order;
create table olading_labor.t_proxy_order
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    customer_id             bigint         not null comment '客户id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    contract_id             bigint         not null comment '合同id',
    proxy_batch_id          bigint         not null comment '代发批次id',
    salary_detail_id        bigint         not null comment '应发工资id',
    name                    varchar(20)    not null comment '姓名',
    id_card                 varchar(20)    not null comment '身份证号',
    cellphone               varchar(11)    null comment '手机号',
    bank_card               varchar(24)    null comment '银行卡号',
    amount                  decimal(19, 2) null comment '金额',
    pay_channel             varchar(64)    not null comment '通道编码',
    complete_time           datetime(6)    null comment '完成时间',
    error_code              varchar(64)    null comment '错误码',
    last_error_info         varchar(256)   null comment '错误信息',
    status                  varchar(20)    not null comment '状态 CHECK_SUCC/CHECK_FAIL/PROCESSING/REMIT/FAIL/DELETED',
    remark                  varchar(256)   null comment '备注'
)
    comment '工资代发订单';





create table t_corporation_tax_record
(
    id                  bigint auto_increment
        primary key,
    create_time         datetime(6)    null comment '创建时间',
    deleted             bit            null comment '是否已删除',
    deleted_time        datetime(6)    null comment '删除时间',
    modify_time         datetime(6)    null comment '更新时间',
    tenant_id           varchar(20)    null comment '租户编号',
    version             int            not null comment '版本',
    supplier_id        bigint        not null comment '灵工平台id',
    supplier_corporation_id      bigint   not null comment '作业主体ID',
    customer_id        bigint        not null comment '客户id',
    tax_month    date         null comment '缴税月份',
    file_id             varchar(63)  null comment '文件id',
    file_upload_time    datetime     null comment '文件上传时间'
)
    comment '缴税记录表';

create table olading_labor.t_corporation_protocol_temp_step
(
    id               bigint auto_increment comment 'id'
        primary key,
    tenant_id        varchar(20) null comment '终端id',
    version          int         null,
    supplier_id      bigint      null comment '平台id',
    supplier_step_id bigint      null comment '作业主体id',
    template_id      bigint      null comment '协议模板id',
    operate          varchar(32) null comment '操作类型 公章签署、个人签署、抄送',
    sort_by          int         null comment '步骤排序',
    step_name        varchar(32) null comment '步骤名',
    create_time      datetime    null comment '创建时间',
    modify_time      datetime    null comment '更新时间'
)
    comment '协议模板步骤信息';



-- ✅ 1. 人员作业主体月应发累计表


CREATE TABLE t_labor_corporation_month_income_summary (
                                                          id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                                          create_time DATETIME(6) COMMENT '创建时间',
                                                          modify_time DATETIME(6) COMMENT '修改时间',
                                                          deleted BIT COMMENT '是否逻辑删除',
                                                          tenant_id VARCHAR(20) COMMENT '租户编号',
                                                          version INT NOT NULL COMMENT '数据版本',
                                                          supplier_id BIGINT NOT NULL COMMENT '平台服务商ID',
                                                          supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                                                          supplier_labor_id BIGINT NOT NULL COMMENT '劳务人员ID',
                                                          month DATE NOT NULL COMMENT '统计月份',
                                                          total_income DECIMAL(22,2) NOT NULL COMMENT '累计应发工资',
                                                          total_withholding_tax DECIMAL(22,2) NOT NULL COMMENT '累计应扣税额',
                                                          total_paid_income DECIMAL(22,2) NOT NULL COMMENT '累计实发工资',
                                                          is_reported BIT DEFAULT 0 COMMENT '是否已申报'
) COMMENT='人员作业主体月应发累计表';

-- ✅ 2. 人员作业主体补缴记录表


CREATE TABLE t_labor_corporation_tax_reissue (
                                                 id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                                 create_time DATETIME(6) COMMENT '创建时间',
                                                 modify_time DATETIME(6) COMMENT '修改时间',
                                                 tenant_id VARCHAR(20) COMMENT '租户编号',
                                                 version INT NOT NULL COMMENT '数据版本',
                                                 supplier_id BIGINT NOT NULL COMMENT '服务商ID',
                                                 supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                                                 supplier_labor_id BIGINT NOT NULL COMMENT '劳务人员ID',
                                                 income_month DATE NOT NULL COMMENT '收入月份',
                                                 notify_from_tax BIT DEFAULT 1 COMMENT '是否税局推送',
                                                 actual_income DECIMAL(22,2) NOT NULL COMMENT '税局实际收入',
                                                 platform_reported_income DECIMAL(22,2) COMMENT '平台已申报收入',
                                                 extra_income DECIMAL(22,2) COMMENT '应补报收入差额',
                                                 supplement_tax DECIMAL(22,2) COMMENT '需补缴税额',
                                                 supplement_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '补缴状态'
) COMMENT='人员作业主体补缴记录表';

-- ✅ 3. 个税申报记录表
CREATE TABLE t_tax_income_declare_record (
                                             id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                             create_time DATETIME(6) COMMENT '创建时间',
                                             modify_time DATETIME(6) COMMENT '修改时间',
                                             tenant_id VARCHAR(20) COMMENT '租户编号',
                                             version INT NOT NULL COMMENT '数据版本号',

                                             supplier_id BIGINT NOT NULL COMMENT '平台服务商ID',
                                             supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',

                                             declare_month DATE NOT NULL COMMENT '个税申报所属月份（如2025-06）',
                                             period_start DATE NOT NULL COMMENT '统计周期开始',
                                             period_end DATE NOT NULL COMMENT '统计周期结束',

                                             declared_people_count INT DEFAULT 0 COMMENT '本次个税申报人数',
                                             declared_income_total DECIMAL(22,2) DEFAULT 0 COMMENT '本次申报应发工资总额',
                                             declared_withholding_tax DECIMAL(22,2) DEFAULT 0 COMMENT '本次申报代扣个税总额',
                                             is_supplement BIT DEFAULT 0 COMMENT '是否为税局归集的补缴申报',

                                             file_id VARCHAR(64) COMMENT '生成的申报文件ID（如zip/excel）',
                                             generate_status VARCHAR(20) DEFAULT 'GENERATED' COMMENT '状态：GENERATED/SUBMITTED/FAILED',
                                             submit_time DATETIME(6) COMMENT '提交时间',
                                             generated_by BIGINT COMMENT '操作人ID',
                                             remark VARCHAR(200) COMMENT '备注说明'
) COMMENT='个税申报生成记录表';

-- ✅ 4. 增值税申报记录表
CREATE TABLE t_tax_vat_declare_record (
                                          id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                          create_time DATETIME(6) COMMENT '创建时间',
                                          modify_time DATETIME(6) COMMENT '修改时间',
                                          tenant_id VARCHAR(20) COMMENT '租户编号',
                                          version INT NOT NULL COMMENT '数据版本号',

                                          supplier_id BIGINT NOT NULL COMMENT '平台服务商ID',
                                          supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',

                                          declare_month DATE NOT NULL COMMENT '增值税申报所属月份（如2025-06）',
                                          period_start DATE NOT NULL COMMENT '统计周期开始',
                                          period_end DATE NOT NULL COMMENT '统计周期结束',

                                          declared_people_count INT DEFAULT 0 COMMENT '本次增值税申报人数',
                                          declared_income_total DECIMAL(22,2) DEFAULT 0 COMMENT '本次申报服务收入总额',
                                          declared_vat_total DECIMAL(22,2) DEFAULT 0 COMMENT '本次申报应缴增值税',
                                          declared_surtax_total DECIMAL(22,2) DEFAULT 0 COMMENT '本次申报附加税合计',
                                          is_supplement BIT DEFAULT 0 COMMENT '是否为补缴申报',

                                          file_id VARCHAR(64) COMMENT '生成的申报文件ID',
                                          generate_status VARCHAR(20) DEFAULT 'GENERATED' COMMENT '状态：GENERATED/SUBMITTED/FAILED',
                                          submit_time DATETIME(6) COMMENT '提交时间',
                                          generated_by BIGINT COMMENT '操作人ID',
                                          remark VARCHAR(200) COMMENT '备注信息'
) COMMENT='增值税申报生成记录表';

-- ✅ 5. 人员信息报送表
CREATE TABLE t_person_info_submission (
                                          id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                          create_time DATETIME(6) COMMENT '创建时间',
                                          modify_time DATETIME(6) COMMENT '修改时间',
                                          tenant_id VARCHAR(20) COMMENT '租户编号',
                                          version INT NOT NULL COMMENT '数据版本',
                                          supplier_id BIGINT NOT NULL COMMENT '服务商ID',
                                          supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                                          supplier_labor_id BIGINT NOT NULL COMMENT '劳务人员ID',
                                          report_quarter VARCHAR(10) NOT NULL COMMENT '报送季度（如2025Q2）',
                                          name VARCHAR(50) COMMENT '人员姓名',
                                          id_card VARCHAR(30) COMMENT '身份证号',
                                          cellphone VARCHAR(11) COMMENT '手机号',
                                          user_code VARCHAR(64) COMMENT '平台内唯一标识码',
                                          real_name_verified BIT DEFAULT 0 COMMENT '是否已实名核验',
                                          is_submitted BIT DEFAULT 0 COMMENT '是否已提交报送',
                                          submit_time DATETIME(6) COMMENT '提交时间',
                                          file_id VARCHAR(64) COMMENT '生成文件ID'
) COMMENT='人员身份信息季度报送表';

-- ✅ 6. 人员收入报送表
CREATE TABLE t_income_info_submission (
                                          id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                          create_time DATETIME(6) COMMENT '创建时间',
                                          modify_time DATETIME(6) COMMENT '修改时间',
                                          tenant_id VARCHAR(20) COMMENT '租户编号',
                                          version INT NOT NULL COMMENT '数据版本',
                                          supplier_id BIGINT NOT NULL COMMENT '服务商ID',
                                          supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                                          supplier_labor_id BIGINT NOT NULL COMMENT '劳务人员ID',
                                          report_quarter VARCHAR(10) NOT NULL COMMENT '报送季度（如2025Q2）',
                                          income_total DECIMAL(22,2) NOT NULL COMMENT '本季度收入合计',
                                          tax_paid DECIMAL(22,2) DEFAULT 0 COMMENT '已扣缴税额',
                                          income_source VARCHAR(100) COMMENT '收入来源平台',
                                          submitted BIT DEFAULT 0 COMMENT '是否已提交',
                                          submit_time DATETIME(6) COMMENT '提交时间',
                                          file_id VARCHAR(64) COMMENT '报送文件ID'
) COMMENT='人员收入信息季度报送表';

-- ✅ 7. 企业信息报送表
CREATE TABLE t_customer_info_submission (
                                            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                            create_time DATETIME(6) COMMENT '创建时间',
                                            modify_time DATETIME(6) COMMENT '修改时间',
                                            tenant_id VARCHAR(20) COMMENT '租户编号',
                                            version INT NOT NULL COMMENT '数据版本',
                                            supplier_id BIGINT NOT NULL COMMENT '服务商ID',
                                            supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                                            report_quarter VARCHAR(10) NOT NULL COMMENT '报送季度（如2025Q2）',
                                            domain_name VARCHAR(64) COMMENT '平台域名',
                                            business_type VARCHAR(50) COMMENT '平台业务类型',
                                            uscc VARCHAR(30) COMMENT '统一社会信用代码',
                                            enterprise_name VARCHAR(100) COMMENT '企业名称',
                                            has_icp_license BIT COMMENT '是否拥有ICP许可或电信许可',
                                            contact_person VARCHAR(50) COMMENT '联系人',
                                            contact_phone VARCHAR(30) COMMENT '联系人电话',
                                            submit_status VARCHAR(20) DEFAULT 'GENERATED' COMMENT '报送状态（GENERATED/SUBMITTED）',
                                            file_id VARCHAR(64) COMMENT '报送文件ID',
                                            submit_time DATETIME(6) COMMENT '提交时间'
) COMMENT='企业信息季度报送表';



CREATE TABLE t_submission_record (
                                     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                                     create_time DATETIME(6) COMMENT '创建时间',
                                     modify_time DATETIME(6) COMMENT '修改时间',
                                     tenant_id VARCHAR(20) COMMENT '租户编号',
                                     version INT NOT NULL COMMENT '数据版本',
                                     supplier_id BIGINT NOT NULL COMMENT '服务商ID',
                                     supplier_corporation_id BIGINT NOT NULL COMMENT '作业主体ID',
                                     report_quarter VARCHAR(10) NOT NULL COMMENT '报送季度（如2025Q2）',
                                     submission_type VARCHAR(20) NOT NULL COMMENT '报送类型（如人员信息报送、企业信息报送）',
                                     submission_info_id BIGINT NOT NULL COMMENT '报送类型（如人员信息报送、企业信息报送）',
                                     submission_info longtext NOT NULL COMMENT '具体报送信息',
                                     submit_status VARCHAR(20) DEFAULT 'GENERATED' COMMENT '报送状态（GENERATED/SUBMITTED）',
                                     file_id VARCHAR(64) COMMENT '报送文件ID',
                                     submit_time DATETIME(6) COMMENT '提交时间'
) COMMENT='报送记录表';


drop table if exists t_sequence;
CREATE TABLE t_sequence
(
    id           VARCHAR(400) NOT NULL,
    create_time  datetime     NULL COMMENT '创建时间',
    modify_time  datetime     NULL COMMENT '更新时间',
    version      INT          NOT NULL COMMENT '版本',
    tenant_id    VARCHAR(20)  NULL COMMENT '租户编号',
    deleted      BIT(1)       NULL COMMENT '是否已删除',
    deleted_time datetime     NULL COMMENT '删除时间',
    next_value   BIGINT       NOT NULL,
    CONSTRAINT pk_t_sequence PRIMARY KEY (id)
) COMMENT '序列表';

drop table if exists olading_labor.t_salary_statement;
CREATE TABLE olading_labor.`t_salary_statement` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '工资表ID',
    `customer_id` BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    `contract_id` BIGINT UNSIGNED NOT NULL COMMENT '服务合同ID',
    `supplier_id` BIGINT UNSIGNED NOT NULL COMMENT '服务商ID',
    `supplier_corporation_id` BIGINT UNSIGNED NOT NULL COMMENT '作业主体ID',
    `tax_period` CHAR(7) NOT NULL COMMENT '税款所属期（格式：2025-06）',
    `total_people` INT UNSIGNED DEFAULT 0 COMMENT '总人数',
    `total_payable` DECIMAL(16,2) DEFAULT 0.00 COMMENT '应发金额总计',
    `total_income_tax` DECIMAL(16,2) DEFAULT 0.00 COMMENT '个税预缴额总计',
    `total_vat` DECIMAL(16,2) DEFAULT 0.00 COMMENT '增值税应纳税额总计',
    `total_surtax` DECIMAL(16,2) DEFAULT 0.00 COMMENT '附加税应纳税额总计',
    `net_payment_total` DECIMAL(16,2) DEFAULT 0.00 COMMENT '实发金额总计',
    `tax_declaration_month` CHAR(7) NOT NULL COMMENT '个税申报月（格式：2025-07）',
    `status` CHAR(30) COMMENT '状态（CALCULATING=算税中,UNCONFIRMED=待确认,CONFIRMED=已确认）',
    `upload_time` DATETIME(6) NOT NULL COMMENT '上传时间',
    `tenant_id` VARCHAR(20) NULL COMMENT '租户编号',
    `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` BIT NULL COMMENT '是否已删除',
    `create_time` DATETIME(6) COMMENT '创建时间',
    `modify_time` DATETIME(6) COMMENT '修改时间',
    `deleted_time` DATETIME(6) COMMENT '删除时间',
    PRIMARY KEY (`id`)
) COMMENT='工资表';

drop table if exists olading_labor.t_salary_detail;
CREATE TABLE `t_salary_detail` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '员工工资明细ID',
                                   `salary_statement_id` bigint NOT NULL COMMENT '工资表ID',
                                   `name` varchar(100) NOT NULL COMMENT '姓名',
                                   `id_card` varchar(18) NOT NULL COMMENT '身份证号',
                                   `phone_number` varchar(20) NOT NULL COMMENT '手机号',
                                   `tax_period` varchar(7) NOT NULL COMMENT '税款所属期 (YYYY-MM)',
                                   `supplier_corporation_id` bigint NOT NULL COMMENT '作业主体ID',

    -- 本期传入数据（每次发薪时填入）
                                   `payable_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '本次应发金额',
                                   `tax_free_income` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '本次免税收入',
                                   `other_deductions` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '本次依法确定的其他扣除',
                                   `tax_relief_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '本次减免税额',

    -- 累计数据（计算后存储，用于下次计算）
                                   `accumulated_income` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
                                   `accumulated_deduction_expenses` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计减除费用 (月数*5000)',
                                   `accumulated_expenses` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计费用（累计收入*20%）',
                                   `accumulated_tax_free_income` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计免税收入 (累加)',
                                   `accumulated_other_deductions` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计依法确定的其他扣除 (累加)',
                                   `accumulated_tax_relief` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计减免税额 (累加)',
                                   `accumulated_taxable_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计应纳税所得额',
                                   `accumulated_tax_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计应纳税额',
                                   `accumulated_prepaid_tax` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计已预缴税额',

    -- 本期计算结果
                                   `current_tax_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '本期应纳税额',
                                   `current_withholding_tax` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '本次应预扣预缴税额（实际扣款）',
                                   `net_payment` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实发金额',

    -- 增值税相关（保留原有字段）
                                   `vat_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '增值税额',
                                   `additional_tax_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '增值附加税额',
                                   `urban_construction_tax` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '城市维护建设附加税',
                                   `education_surcharge` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '教育费附加税',
                                   `local_education_surcharge` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '地方教育附加税',

    -- 审计字段
                                   `tenant_id` VARCHAR(20) NULL COMMENT '租户编号',
                                   `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
                                   `deleted` BIT NULL COMMENT '是否已删除',
                                   `create_time` DATETIME(6) COMMENT '创建时间',
                                   `modify_time` DATETIME(6) COMMENT '修改时间',
                                   `deleted_time` DATETIME(6) COMMENT '删除时间',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_salary_statement_id` (`salary_statement_id`),
                                   KEY `idx_id_card_corporation_period` (`id_card`, `supplier_corporation_id`, `tax_period`),
                                   KEY `idx_corporation_period` (`supplier_corporation_id`, `tax_period`),
                                   KEY `idx_create_time` (`create_time`),
                                   KEY `idx_tenant_deleted` (`tenant_id`, `deleted`)
) COMMENT='工资明细表';





drop table if exists olading_labor.t_previous_income_deduction;
CREATE TABLE olading_labor.`t_previous_income_deduction` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `supplier_id` BIGINT UNSIGNED NOT NULL COMMENT '服务商ID',
    `supplier_corporation_id` BIGINT UNSIGNED NOT NULL COMMENT '作业主体ID',
    `full_name` VARCHAR(100) NOT NULL COMMENT '姓名',
    `id_number` CHAR(18) NOT NULL COMMENT '身份证号',
    `accumulated_income` DECIMAL(16,2) DEFAULT 0.00 COMMENT '累计收入',
    `accumulated_expenses` DECIMAL(16,2) DEFAULT 0.00 COMMENT '累计费用',
    `accumulated_deduction_expenses` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '累计减除费用 (月数*5000)',
    `accumulated_tax_free_income` DECIMAL(16,2) DEFAULT 0.00 COMMENT '累计免税收入',
    `accumulated_other_deductions` DECIMAL(16,2) DEFAULT 0.00 COMMENT '累计依法确定的其他扣除',
    `accumulated_prepaid_tax` DECIMAL(16,2) DEFAULT 0.00 COMMENT '累计已预缴税额',
    `accumulated_tax_reductions` DECIMAL(16,2) DEFAULT 0.00 COMMENT '累计减免税额',
    `tax_period` CHAR(7) NOT NULL COMMENT '税款所属期（格式：2025-06）',
    `upload_time` DATETIME NOT NULL COMMENT '上传时间',
    `tenant_id` VARCHAR(20) NULL COMMENT '租户编号',
    `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` BIT NULL COMMENT '是否已删除)',
    `create_time` DATETIME COMMENT '创建时间',
    `modify_time` DATETIME COMMENT '修改时间',
    `deleted_time` DATETIME COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上期收入减除表';



drop table if exists olading_labor.t_personal_income_tax_declare;
create table olading_labor.t_personal_income_tax_declare
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    tax_payment_period      varchar(20) null comment '税款所属期',
    income_tax_month        varchar(20) null comment '个税申报月',
    taxpayers_count         varchar(20) null comment '纳税人数',
    current_income          varchar(64) null comment '本期收入',
    status                  varchar(20) null comment '生成状态',
    tax_status              varchar(20) null comment '申报状态'
)
    comment '税务个税申报表';

drop table if exists olading_labor.t_personal_income_tax_detail;
CREATE TABLE olading_labor.t_personal_income_tax_detail
(
    id                                 BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    create_time                        DATETIME(6) COMMENT '创建时间',
    deleted                            BIT COMMENT '是否已删除',
    deleted_time                       DATETIME(6) COMMENT '删除时间',
    modify_time                        DATETIME(6) COMMENT '更新时间',
    tenant_id                          VARCHAR(20) COMMENT '租户编号',
    version                            INT         NOT NULL COMMENT '版本',
    supplier_id                        BIGINT      NOT NULL COMMENT '灵工平台id',
    supplier_corporation_id            BIGINT      NOT NULL COMMENT '作业主体ID',
    tax_declare_id                     BIGINT      NOT NULL COMMENT '申报ID',
    name                               VARCHAR(50) NOT NULL COMMENT '姓名',
    id_card                            VARCHAR(20) NOT NULL COMMENT '身份证号',
    cellphone                          VARCHAR(20) NOT NULL COMMENT '手机号',
    current_income                     DECIMAL(22, 2) DEFAULT 0.00 COMMENT '本期收入',
    actual_amount                      DECIMAL(22, 2) DEFAULT 0.00 COMMENT '实发金额',
    accumulated_income                 DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计收入',
    accumulated_expenses               DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计费用',
    accumulated_tax_free_income        DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计免税收入',
    accumulated_tax_deduction_expenses DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计减除费用',
    accumulated_other_deductions       DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计依法确定的其他扣除',
    accumulated_prepaid_tax            DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计已预缴税额',
    accumulated_tax_reductions         DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计减免税额',
    accumulated_taxable_amount         DECIMAL(22, 2) DEFAULT 0.00 COMMENT '累计应纳税额',
    current_withholding_tax            DECIMAL(22, 2) DEFAULT 0.00 COMMENT '本期应预扣预缴税额',
    tax_period                         VARCHAR(20) NOT NULL COMMENT '税款所属期',
    declare_month                      VARCHAR(20) NOT NULL COMMENT '申报月份'
) COMMENT ='税务个税申报明细表';


drop table if exists olading_labor.t_value_added_tax_declare;
create table olading_labor.t_value_added_tax_declare
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    tax_payment_period      varchar(20) null comment '税款所属期',
    income_tax_month        varchar(20) null comment '个税申报月',
    taxpayers_count         varchar(20) null comment '纳税人数',
    current_income          varchar(64) null comment '本期收入',
    status                  varchar(20) null comment '生成状态',
    tax_status              varchar(20) null comment '申报状态'
)
    comment '税务增值税申报表';

drop table if exists olading_labor.t_value_added_tax_detail;
CREATE TABLE olading_labor.t_value_added_tax_detail
(
    id                           BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    create_time                  DATETIME(6) COMMENT '创建时间',
    deleted                      BIT COMMENT '是否已删除',
    deleted_time                 DATETIME(6) COMMENT '删除时间',
    modify_time                  DATETIME(6) COMMENT '更新时间',
    tenant_id                    VARCHAR(20) COMMENT '租户编号',
    version                      INT         NOT NULL COMMENT '版本',
    supplier_id                  BIGINT      NOT NULL COMMENT '灵工平台id',
    supplier_corporation_id      BIGINT      NOT NULL COMMENT '作业主体ID',
    value_added_tax_id           BIGINT      NOT NULL COMMENT '增值税申报ID',
    tax_period                   VARCHAR(20) NOT NULL COMMENT '税款所属期',
    declare_month                VARCHAR(20) NOT NULL COMMENT '申报月份',
    -- 从业人员信息模块
    name                         VARCHAR(50) COMMENT '姓名',
    certificate_type             VARCHAR(20) COMMENT '证件类型',
    id_card                      VARCHAR(20) COMMENT '证件号码',
    country_region               VARCHAR(50) COMMENT '国家或地区',
    address                      VARCHAR(200) COMMENT '地址',
    user_name                    VARCHAR(50) COMMENT '用户名称',
    user_unique_code             VARCHAR(64) COMMENT '用户唯一标识码',
    -- 增值税模块
    tax_basis                    DECIMAL(22, 2) DEFAULT 0.00 COMMENT '计税依据 (增值税)',
    tax_item                     VARCHAR(50) COMMENT '征收品目 (增值税)',
    tax_rate                     VARCHAR(20) COMMENT '征收率 (增值税)',
    vat_amount                   DECIMAL(22, 2) DEFAULT 0.00 COMMENT '本期应纳税额 (增值税)',
    vat_exemption_code           VARCHAR(20) COMMENT '减免性质代码 (增值税)',
    vat_exemption_amount         DECIMAL(22, 2) DEFAULT 0.00 COMMENT '减免税额 (增值税)',
    vat_payable                  DECIMAL(22, 2) DEFAULT 0.00 COMMENT '应补(退)税额 (增值税)',
    -- 城市维护建设税模块
    urban_tax_rate               VARCHAR(20) COMMENT '适用税率 (城市维护建设税)',
    urban_tax_amount             DECIMAL(22, 2) DEFAULT 0.00 COMMENT '本期应纳税额 (城市维护建设税)',
    urban_exemption_code         VARCHAR(20) COMMENT '减免性质代码 (城市维护建设税)',
    urban_exemption_amount       DECIMAL(22, 2) DEFAULT 0.00 COMMENT '减免税额 (城市维护建设税)',
    urban_tax_payable            DECIMAL(22, 2) DEFAULT 0.00 COMMENT '应补(退)税额 (城市维护建设税)',
    -- 教育附加模块
    edu_tax_rate                 VARCHAR(20) COMMENT '适用税率 (教育附加)',
    edu_tax_amount               DECIMAL(22, 2) DEFAULT 0.00 COMMENT '本期应纳税额 (教育附加)',
    edu_exemption_code           VARCHAR(20) COMMENT '减免性质代码 (教育附加)',
    edu_exemption_amount         DECIMAL(22, 2) DEFAULT 0.00 COMMENT '减免税额 (教育附加)',
    edu_tax_payable              DECIMAL(22, 2) DEFAULT 0.00 COMMENT '应补(退)税额 (教育附加)',
    -- 地方教育附加模块
    local_edu_tax_rate           VARCHAR(20) COMMENT '适用税率 (地方教育附加)',
    local_edu_tax_amount         DECIMAL(22, 2) DEFAULT 0.00 COMMENT '本期应纳税额 (地方教育附加)',
    local_edu_exemption_code     VARCHAR(20) COMMENT '减免性质代码 (地方教育附加)',
    local_edu_exemption_amount   DECIMAL(22, 2) DEFAULT 0.00 COMMENT '减免税额 (地方教育附加)',
    local_edu_tax_payable        DECIMAL(22, 2) DEFAULT 0.00 COMMENT '应补(退)税额 (地方教育附加)'
) COMMENT='税务增值税申报明细表';


drop table if exists olading_labor.t_tax_payment_voucher;
create table olading_labor.t_tax_payment_voucher
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    tax_payment_period      varchar(20) null comment '税款所属期',
    file_ids                varchar(5000) null comment '附件id,逗号分隔'
)
    comment '税务税款缴纳表';


drop table if exists olading_labor.t_info_submission_labor;
create table olading_labor.t_info_submission_labor
(
    id                               bigint auto_increment
        primary key,
    create_time                      datetime(6)  null comment '创建时间',
    deleted                          bit          null comment '是否已删除',
    deleted_time                     datetime(6)  null comment '删除时间',
    modify_time                      datetime(6)  null comment '更新时间',
    tenant_id                        varchar(20)  null comment '租户编号',
    version                          int          not null comment '版本',
    supplier_id                      bigint       not null comment '灵工平台id',
    supplier_corporation_id          bigint       not null comment '作业主体id',
    report_status                    varchar(20)  null comment '报送状态',
    registration_license_obtained    varchar(20)  null comment '是否已取得登记证照',
    name                             varchar(20)  null comment '名称（姓名）',
    unified_social_credit_code       varchar(64)  null comment '统一社会信用代码（纳税人识别号）',
    professional_service_agency_flag varchar(64)  null comment '专业服务机构标识',
    labor_name                       varchar(20)  null comment '姓名',
    certificate_type                 VARCHAR(20)  null comment '证件类型',
    id_card                          VARCHAR(20)  null comment '身份证号',
    household_city                   varchar(50)  null comment '国家或地区',
    income_reporting_exemption_flag  varchar(64)  null comment '是否存在免于报送收入信息情形',
    exemption_type                   varchar(20)  null comment '免报类型',
    household_address                varchar(256) null comment '地址',
    store_name                       varchar(64)  null comment '店铺（用户）名称',
    store_unique_code                varchar(128) null comment '店铺（用户）唯一标识码',
    website_url                      varchar(64)  null comment '网址链接（选填）',
    card_bank                        varchar(64)  null comment '开户银行/非银行支付机构',
    account_name                     varchar(64)  null comment '账户名称',
    bank_card                        varchar(64)  null comment '银行账号/支付账户',
    contact_name                     varchar(20)  null comment '联系人姓名',
    contact_phone                    varchar(20)  null comment '联系电话',
    start_date                       varchar(20)  null comment '经营开始时间',
    end_date                         varchar(20)  null comment '经营结束时间',
    info_status_flag                 varchar(20)  null comment '信息状态标识'
)
    comment '人员信息报送表';



drop table if exists olading_labor.t_info_submission_labor_income;
create table olading_labor.t_info_submission_labor_income
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    start_date              varchar(20)  null comment'开始日期',
    end_date                varchar(20)  null comment '结束日期',
    status                  varchar(20) null comment '生成状态',
    file_id                 varchar(64) null comment '附件id'
)
    comment '人员收入信息报送表';



drop table if exists olading_labor.t_info_submission_enterprise;
create table olading_labor.t_info_submission_enterprise
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)  null comment '创建时间',
    deleted                 bit          null comment '是否已删除',
    deleted_time            datetime(6)  null comment '删除时间',
    modify_time             datetime(6)  null comment '更新时间',
    tenant_id               varchar(20)  null comment '租户编号',
    version                 int          not null comment '版本',
    supplier_id             bigint       not null comment '灵工平台id',
    supplier_corporation_id bigint       not null comment '作业主体id',
    report_status           varchar(20)  null comment '报送状态',
    name                    varchar(200) not null comment '名称（姓名）',
    social_credit_code      varchar(64)  null comment '统一社会信用代码（纳税人识别号）',
    platform_name           varchar(200) null comment '平台内的平台名称',
    platform_unique_code    varchar(200) null comment '平台内的平台唯一标识码',
    start_date              varchar(20)  null comment '经营开始时间',
    end_date                varchar(20)  null comment '经营结束时间',
    info_status_flag        varchar(20)  null comment '信息状态标识'
)
    comment '企业信息报送表';


























