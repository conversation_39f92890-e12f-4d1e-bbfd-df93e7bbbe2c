package com.olading.operate.labor.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.provider.OladingEnvironmentProvider;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationData;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelData;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SurtaxCodeEnum;
import com.olading.operate.labor.domain.corporation.SurtaxData;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import com.olading.operate.labor.domain.proxy.ChannelRemitProvider;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.CustomerManager;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.InfoManager;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.domain.supplier.SmsBusinessType;
import com.olading.operate.labor.domain.supplier.SupplierData;
import com.olading.operate.labor.domain.supplier.SupplierDomainData;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import com.olading.operate.labor.domain.supplier.SupplierPayChannelData;
import com.olading.operate.labor.domain.supplier.SupplierSmsTemplateEntity;
import com.olading.operate.labor.util.crypto.HashPassword;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Transactional
@RequiredArgsConstructor
@Service
public class SupplierService {

    public static final TenantInfo DEFAULT_TENANT = TenantInfo.nobody(TenantInfo.TenantType.SUPPLIER);

    private final CorporationManager corporationManager;
    private final SupplierManager supplierManager;
    private final UserManager userManager;
    private final AuthorityManager authorityManager;
    private final OladingEnvironmentProvider environmentProvider;
    private final InfoManager infoManager;
    private final CustomerManager customerManager;
    private final BusinessContractManager businessContractManager;
    private final ChannelRemitProvider channelRemitProvider;

    public UserEntity login(String cellphone, String password, long supplierId, boolean isCheck) {
        UserEntity user = userManager.getUserByCellphone(TenantInfo.ofSupplier(supplierId), cellphone);
        if (user == null) {
            throw new BusinessException("登录失败");
        }
        if (isCheck  && !HashPassword.checkPassword(password, user.getPassword())) {
            throw new BusinessException("密码错误或用户不存在");
        }
        if (getUserSupplierMember(supplierId,user.getId()) == null) {
            throw new BusinessException("没有可登录灵工平台");
        }
        return user;
    }


    /**
     * 获取用户可登录的服务商
     */
    public SupplierMemberEntity getUserSupplierMember(long supplierId, long userId) {
        final SupplierMemberEntity member = supplierManager.getSupplierMemberByUserId(supplierId, userId);
        if(member == null || member.getDisabled()){
            return null;
        }
        return member;
    }


    /**
     * 获取用户可登录的服务商
     */
    public List<SupplierEntity> getUserAvailableSupplier(long userId) {
        List<SupplierMemberEntity> members = supplierManager.getSupplierMemberByUserId(userId)
                .stream().filter(o -> !o.getDisabled()).toList();
        List<SupplierEntity> list = supplierManager.fetchSupplier(t -> t.id.in(members.stream().map(SupplierMemberEntity::getSupplierId).collect(Collectors.toSet())));
        return list.stream().filter(o -> !o.getDisabled()).collect(Collectors.toList());
    }

    public void useSupplierMember(long supplierId, long userId) {
        supplierManager.useSupplierMember(supplierId, userId);
    }



    /**
     * 开通一个服务商
     */
    public void addSupplier(SupplierData data,TenantInfo tenantInfo) {
        SupplierEntity supplier = supplierManager.addSupplier(data,tenantInfo);
        supplierManager.saveSupplierInfo(supplier.getId(), data.getInfo());
        if(data.getDomain() != null){
            data.getDomain().setSupplierId(supplier.getId());
            supplierManager.saveSupplierDomain(data.getDomain());
        }
        if (StringUtils.isNotBlank(data.getInfo().getContactPhone())) {
            setAdmin(supplier.getId(), data.getInfo().getContactPhone(), data.getInfo().getContacts());
        }
    }

    public void editSupplier(SupplierData data) {
        SupplierEntity supplier = supplierManager.requireSupplier(data.getId());
        supplierManager.saveSupplier(data);
        if(data.getDomain() != null){
            data.getDomain().setSupplierId(supplier.getId());
            supplierManager.saveSupplierDomain(data.getDomain());
        }
        supplierManager.saveSupplierInfo(supplier.getId(), data.getInfo());
        if (StringUtils.isNotBlank(data.getInfo().getContactPhone())) {
            setAdmin(supplier.getId(), data.getInfo().getContactPhone(), data.getInfo().getContacts());
        }
    }

    public void setSupplierDisabled(long supplierId, boolean disabled) {
        supplierManager.setSupplierDisabled(supplierId, disabled);
    }

    /**
     * 设置管理员
     */
    public void setAdmin(long supplierId, String cellphone, String name) {

        var member = addMember(supplierId, cellphone, name);

        setAdmin(supplierId, member.getId());
    }

    public void setAdmin(long supplierId, long memberId) {
        SupplierMemberEntity member = supplierManager.requireMember(supplierId, memberId);
        supplierManager.setAdmin(supplierId, member);
    }

    private SupplierMemberEntity addMember(long supplierId, String cellphone, String name) {
        UserEntity user = saveUser(TenantInfo.ofSupplier(supplierId),cellphone, name);
        return supplierManager.addMember(supplierId, user, name);
    }

    public void disableMember(long supplierId, long memberId, boolean disabled) {
        supplierManager.setMemberDisabled(supplierId, memberId, disabled);
    }

    private UserEntity saveUser(TenantInfo tenantInfo, String cellphone, String name) {
        UserEntity user = userManager.addUser(tenantInfo, cellphone, name);
        PersonInfoData info = new PersonInfoData();
        info.setCellphone(cellphone);
        info.setName(name);
        userManager.setUserInfo(user.getId(), info);
        return user;
    }

    public SupplierData requireSupplier(long id) {
        SupplierEntity supplier = supplierManager.requireSupplier(id);
        EnterpriseInfoEntity info = supplierManager.getSupplierInfo(supplier.getId());
        final SupplierDomainData supplierDomain = supplierManager.getSupplierDomain(id);
        SupplierData data = new SupplierData(supplier);
        if (info != null) {
            data.setInfo(new EnterpriseInfoData(info));
        }
        data.setDomain(supplierDomain);
        final SupplierMemberEntity supplierMember = supplierManager.getSupplierMemberByUserId(supplier.getId(), supplier.getAdminUserId());
        data.setAdminName(supplierMember.getName());
        return data;
    }

    /**
     * 服务商开通一个业务主体
     */
    public SupplierCorporationEntity addCorporation(TenantInfo tenantInfo, CorporationData data) {

        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(data.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }
        // 创建业务主体
        final SupplierCorporationEntity corporationEntity = corporationManager.saveCorporation(tenantInfo, data);

        if (CollectionUtil.isNotEmpty(data.getRoleData())){
            data.getRoleData().forEach(o-> authorityManager.addRoleDataScopes(tenantInfo, o.getId(),new OwnedByFragment(OwnerType.CORPORATION, corporationEntity.getId())));
        }
        return corporationEntity;
    }

    /**
     * 编辑业务主体基本信息
     */
    public void editCorporation(TenantInfo tenantInfo, CorporationData data) {

        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(data.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }
        corporationManager.requireCorporation(data.getId());
        // 编辑业务主体基本信息
        corporationManager.saveCorporation(tenantInfo,data);
        authorityManager.editDataScope(tenantInfo, new OwnedByFragment(OwnerType.CORPORATION, data.getId()), data.getRoleData());

    }

    /**
     * 编辑作业主体业务配置
     * @param tenantInfo
     * @param data
     */
    public void editCorporationBusiness(TenantInfo tenantInfo, CorporationData data) {
        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(data.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }
        corporationManager.requireCorporation(data.getId());
        if(data.getConfigData() != null){
            //发票分类格式校验
            data.getConfigData().getInvoiceCategoryList();
            //附加增加税配置校验
            final List<SurtaxData> surtax = data.getConfigData().getSurtax();
            final List<SurtaxData> initSurtaxData = SurtaxCodeEnum.getInitSurtaxData();
            final Map<String, SurtaxData> surtaxDataMap = initSurtaxData.stream().collect(Collectors.toMap(SurtaxData::getName, o -> o));
            surtax.forEach(o->{
                if(surtaxDataMap.get(o.getName()) != null){
                    surtaxDataMap.get(o.getName()).setRate(o.getRate());
                    surtaxDataMap.get(o.getName()).setDiscount_rate(o.getDiscount_rate());
                }
            });
            data.getConfigData().setSurtaxData(JSONUtil.toJsonStr(initSurtaxData));
            corporationManager.saveCorporationConfig(tenantInfo, data);
        }
        if(StringUtils.isNotBlank(data.getDefaultPayChannel())){
            corporationManager.setDefaultPayChannel(tenantInfo,data.getId(), data.getDefaultPayChannel());
        }

    }


    public void addRole(long supplierId, String name, String code, String remark, List<String> authorities) {
        SupplierData supplier = requireSupplier(supplierId);
        RoleData data = new RoleData();
        data.setName(name);
        data.setRemark(remark);
        data.setCode(code);
        RoleEntity role = authorityManager.addRole(TenantInfo.ofSupplier(supplier.getId()), data);
        authorityManager.setRoleAuthorities(role.getId(), authorities);
    }

    public void disableRole(long supplierId, long roleId, boolean  disabled) {
        requireRole(supplierId, roleId);
        RoleEntity role = authorityManager.requireRole(roleId);
        if (role != null) {
            authorityManager.disableRole(roleId,disabled);
        }
    }

    public void editRole(TenantInfo tenantInfo, Long supplierId, RoleData data, List<String> authorities) {
        requireSupplier(supplierId);
        authorityManager.requireRole(data.getId());
        authorityManager.editRole(tenantInfo, data);
        authorityManager.setRoleAuthorities(data.getId(), authorities);
    }

    public void deleteRole(TenantInfo tenantInfo,Long supplierId, long roleId) {
        requireSupplier(supplierId);
        authorityManager.requireRole(roleId);
        authorityManager.deleteRole(tenantInfo, roleId);
    }

    /**
     * 设置角色的成员
     */
    public void setRoleMembers(long supplierId, long roleId, List<Long> memberId) {

        // 检查角色
        RoleEntity role = requireRole(supplierId, roleId);

        var members = supplierManager.getSupplierMember(supplierId, memberId);

        authorityManager.setRoleMembers(role.getId(), members.stream().map(SupplierMemberEntity::getId).collect(Collectors.toList()));
    }

    /**
     * 设置角色的成员
     */
    public void setMemberRoles(long supplierId, long memberId, Set<Long> roleIds) {

        // 检查角色
        requireMember(supplierId, memberId);
        authorityManager.memberSetRoles(memberId, roleIds);
    }

    public RoleEntity requireRole(long supplierId, long roleId) {
        RoleEntity role = authorityManager.requireRole(roleId);
        if (!role.getTenant().equals(TenantInfo.ofSupplier(supplierId))) {
            throw new BusinessException("无此角色");
        }
        return role;
    }

    public SupplierMemberEntity requireMember(long supplierId, long memberId) {
        return supplierManager.requireMember(supplierId, memberId);
    }

    public SupplierMemberEntity requireMemberByUserId(long supplierId, long userId) {
        var member = supplierManager.getSupplierMemberByUserId(supplierId, userId);
        if (member == null) {
            throw new BusinessException("无此人员");
        }
        return member;
    }

    public void editSupplierMember(long supplierId, long memberId, String cellphone, String name, Set<Long> roleIds) {
        var supplier = requireSupplier(supplierId);
        var member = supplierManager.requireMember(supplierId, memberId);

        //if (Objects.equals(supplier.getAdminUserId(), member.getUserId())) {
        //    throw new BusinessException("不允许修改管理员手机号");
        //}

        if(!Objects.equals(member.getCellphone(), cellphone)){
            throw new BusinessException("手机号不允许变更");
        }

        if(!Objects.equals(member.getName(), name)){
            throw new BusinessException("姓名不允许变更");
        }

        // 更新member信息
        supplierManager.editMember(supplierId, member.getId(), name);

        // 变更手机号
        if (cellphone != null && !Objects.equals(member.getCellphone(), cellphone)) {
            var user = saveUser(TenantInfo.ofSupplier(supplierId),cellphone, name);
            var existsMember = supplierManager.getSupplierMemberByUserId(supplierId, user.getId());
            if (existsMember != null && existsMember.getId() != memberId) {
                throw new BusinessException("手机号已存在");
            }
            supplierManager.rebindMember(supplierId, member.getId(), user);
        }

        this.setMemberRoles(supplierId, memberId, roleIds);
    }



    /**
     * 确保服务商创建了默认的角色
     */
    private void ensureSupplierRoles(SupplierEntity supplier) {
        TenantInfo tenant = TenantInfo.ofSupplier(supplier.getId());
        RoleEntity role = authorityManager.getRoleByName(tenant, "业务管理员");
        if (role == null) {
            RoleData data = new RoleData();
            data.setName("业务管理员");
            data.setCode("BIZ_ADMIN");
            role = authorityManager.addRole(tenant, data);
            authorityManager.setRoleAuthorities(role.getId(),new ArrayList<>( Authority.getSupplierAllAuthorities()));
        }
    }

    public boolean IsDisabled(long supplierId) {
        SupplierEntity supplier = supplierManager.getSupplierById(supplierId);
        return supplier != null && supplier.getDisabled();
    }

    /**
     * 发送短信
     * @param cellphone
     * @param parameters
     * @param smsBusinessType
     * @param supplierId
     */
    public void sendSms(String cellphone, Map<String, String> parameters, SmsBusinessType smsBusinessType, Long supplierId){
        final SupplierEntity supplierEntity = supplierManager.requireSupplier(supplierId);
        final SupplierSmsTemplateEntity supplierSmsTemplate = supplierManager.requireSupplierSmsTemplate(supplierId, smsBusinessType);
        environmentProvider.sendSms(cellphone, parameters, supplierSmsTemplate.getTemplateCode(), supplierEntity.getSignatureCode());
    }

    public void changePhone(Long supplierId, Long userId, @NotBlank(message = "手机号不能为空") String phone) {
        supplierManager.requireSupplier(supplierId);
        userManager.changePhone(userId,TenantInfo.ofSupplier(supplierId), phone);
        //变更supplierMember手机号信息
        supplierManager.changeMemberPhone(supplierId, userId,  phone);
    }

    public void addMember(Long supplierId, String name, String cellphone, Set<Long> roleIds ) {
        final SupplierMemberEntity supplierMemberEntity = this.addMember(supplierId, cellphone, name);
        this.setMemberRoles(supplierId, supplierMemberEntity.getId(), roleIds);
    }

    public CorporationData getCorporation(TenantInfo tenantInfo, Long id) {
        final SupplierCorporationEntity corporationEntity = corporationManager.requireCorporation(id);
            if (corporationEntity == null || !Objects.equals(tenantInfo, corporationEntity.getTenant())) {
            throw new IllegalArgumentException("不存在此作业主体");
        }
        final EnterpriseInfoEntity enterpriseInfo = infoManager.getEnterpriseInfo(corporationEntity.getEnterpriseInfoId());
        final List<CorporationPayChannelData> channelEntities = corporationManager.queryPayChannelList(id);
        final CorporationConfigEntity corporationConfigEntity = corporationManager.queryCorporationConfig(id);
        final List<RoleData> roleDataList = authorityManager.getRoleByDataScope(OwnerType.CORPORATION,id);
        return  new CorporationData(corporationEntity,enterpriseInfo,corporationConfigEntity,channelEntities,roleDataList);
    }

    public void setSmsConfig(Long supplierId, SmsBusinessType businessType, String templateCode) {
        final SupplierData supplierData = requireSupplier(supplierId);
        supplierManager.setSmsConfig(supplierId, businessType, templateCode);
    }

    public List<SupplierSmsTemplateEntity> getSmsConfig(Long supplierId) {
        final SupplierData supplierData = requireSupplier(supplierId);
        return supplierManager.requireSupplierSmsTemplate(supplierId);
    }

    public void removeMember(long l, Long id, boolean disabled) {
        final SupplierMemberEntity supplierMemberEntity = supplierManager.requireMember(l, id);
        if(!supplierMemberEntity.getDisabled()){
            throw new BusinessException("请先禁用该用户");
        }
        supplierManager.removeMember(l, id);
        authorityManager.removeRoleMembers(id);
    }

    public Map<OwnerType, Set<Long>> queryAllDataScope(long l) {

        return Map.of(
                OwnerType.CORPORATION, corporationManager.queryCorporationBySupplierId(l).stream().map(SupplierCorporationEntity::getId).collect(Collectors.toSet()),
                OwnerType.CONTRACT,businessContractManager.queryContract(t -> t.supplierId.eq(l)).fetch().stream().map(BusinessContractEntity::getId).collect(Collectors.toSet()),
                OwnerType.CUSTOMER, customerManager.queryCustomer(t -> t.supplierId.eq(l)).fetch().stream().map(CustomerEntity::getId).collect(Collectors.toSet())
        );
    }

    public void corporationConfigPayChannel(TenantInfo tenantInfo,Long supplierId, CorporationPayChannelData data) {
        supplierManager.requireSupplier(Long.parseLong(tenantInfo.getId()));
        final CorporationPayChannelEntity corporationPayChannelEntity = corporationManager.saveCorporationPayChannel(tenantInfo, supplierId, data);
        //通道信息入网 TODO
        //channelRemitProvider.createAccount(corporationPayChannelEntity);
    }

    public List<SupplierPayChannelData> getPayChannelList(long l) {
        final SupplierEntity supplierEntity = supplierManager.requireSupplier(l);
        return supplierManager.queryPayChannelList(supplierEntity.getId());
    }

    public void addPayChannel(Set<SupplierPayChannelData> collect) {
        supplierManager.addPayChannel(collect);
    }
}