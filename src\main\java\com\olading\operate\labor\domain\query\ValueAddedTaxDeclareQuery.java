package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.tax.QValueAddedTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.ValueAddedTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDeclareVo;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

public class ValueAddedTaxDeclareQuery implements EntityQuery<QueryFilter<ValueAddedTaxDeclareQuery.Filters>, ValueAddedTaxDeclareVo> {

    private final QValueAddedTaxDeclareEntity t1 = QValueAddedTaxDeclareEntity.valueAddedTaxDeclareEntity;
    private final QSupplierCorporationEntity t2 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }
        if (filters.getFilters().getSupplierCorporationId() != null) {
            criteria.and(t1.supplierCorporationId.eq(filters.getFilters().getSupplierCorporationId()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getTaxPaymentPeriod())) {
            criteria.and(t1.taxPaymentPeriod.eq(filters.getFilters().getTaxPaymentPeriod()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getIncomeTaxMonth())) {
            criteria.and(t1.incomeTaxMonth.eq(filters.getFilters().getIncomeTaxMonth()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getSupplierCorporationName())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getSupplierCorporationName())));
        }

        if (filters.getFilters().getCreateTimeStart() != null) {
            criteria.and(t1.createTime.goe(filters.getFilters().getCreateTimeStart()));
        }

        if (filters.getFilters().getCreateTimeEnd() != null) {
            criteria.and(t1.createTime.lt(filters.getFilters().getCreateTimeEnd()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getStatus())) {
            criteria.and(t1.status.eq(filters.getFilters().getStatus()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getTaxStatus())) {
            criteria.and(t1.taxStatus.eq(filters.getFilters().getTaxStatus()));
        }

        query.select(t1, t2)
                .from(t1)
                .leftJoin(t2).on(t1.supplierCorporationId.eq(t2.id))
                .where(criteria);
    }

    @Override
    public ValueAddedTaxDeclareVo transform(Object v) {
        Tuple tuple = (Tuple) v;
        ValueAddedTaxDeclareEntity entity = tuple.get(t1);
        SupplierCorporationEntity corporation = tuple.get(t2);
        
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        BeanUtils.copyProperties(entity, vo);
        
        if (corporation != null) {
            vo.setSupplierCorporationName(corporation.getName());
        }
        
        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private Long supplierCorporationId;
        private String taxPaymentPeriod;
        private String incomeTaxMonth;
        private String supplierCorporationName;
        private String status;
        private String taxStatus;
        private LocalDateTime createTimeStart;
        private LocalDateTime createTimeEnd;
    }
}
