package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.proxy.ProxyOrderBatchDetailData;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchEntity;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchStatusEnum;
import com.olading.operate.labor.domain.proxy.order.QProxyBatchEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

public class ProxyBatchQuery implements EntityQuery<QueryFilter<ProxyBatchQuery.Filters>, ProxyOrderBatchDetailData> {

    private final QProxyBatchEntity t1 = QProxyBatchEntity.proxyBatchEntity;
    private final QSupplierCorporationEntity t2 = QSupplierCorporationEntity.supplierCorporationEntity;
    private final QCustomerEntity t3 = QCustomerEntity.customerEntity;
    private final QBusinessContractEntity t4 = QBusinessContractEntity.businessContractEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.in(filters.getFilters().getId()));
        }
        if (filters.getFilters().getSupplierId() != null){
        criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getCorporation())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getCorporation())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getBusinessContract())) {
            criteria.and(t4.name.like(JpaUtils.fullLike(filters.getFilters().getBusinessContract())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getCustomer())) {
            criteria.and(t3.name.like(JpaUtils.fullLike(filters.getFilters().getCustomer())));
        }

        if (filters.getFilters().getBatchStatus() != null) {
            criteria.and(t1.batchStatus.eq(filters.getFilters().getBatchStatus()));
        }
        if (filters.getFilters().getCorporationIds() != null){
            criteria.and(t1.supplierCorporationId.in(filters.getFilters().getCorporationIds()));
        }
        if (filters.getFilters().getContractIds() != null){
            criteria.and(t1.contractId.in(filters.getFilters().getContractIds()));
        }
        if (filters.getFilters().getStatementId() != null){
            criteria.and(t1.salaryStatementId.eq(filters.getFilters().getStatementId()));
        }

        query.select(t1, t2, t3, t4)
                .from(t1)
                .leftJoin(t2).on(t1.supplierCorporationId.eq(t2.id))
                .leftJoin(t3).on(t1.customerId.eq(t3.id))
                .leftJoin(t4).on(t1.contractId.eq(t4.id));
        query.where(criteria);
    }

    @Override
    public ProxyOrderBatchDetailData transform(Object v) {
        Tuple tuple = (Tuple) v;
        ProxyBatchEntity proxyBatch = tuple.get(t1);
        ProxyOrderBatchDetailData proxyOrderBatchDetailData = ProxyOrderBatchDetailData.of(proxyBatch);
        SupplierCorporationEntity supplierCorporationEntity = tuple.get(t2);
        proxyOrderBatchDetailData.setCorporation(supplierCorporationEntity.getName());
        CustomerEntity customerEntity = tuple.get(t3);
        proxyOrderBatchDetailData.setCustomer(customerEntity.getName());
        BusinessContractEntity businessContractEntity = tuple.get(t4);
        proxyOrderBatchDetailData.setBusinessContract(businessContractEntity.getName());
        return proxyOrderBatchDetailData;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    @Builder
    public static class Filters {

        private Long id;

        private Long supplierId;

        private String corporation;

        private Long statementId;

        private String businessContract;

        private String customer;

        private Set<Long> corporationIds;

        private Set<Long> contractIds;

        private ProxyBatchStatusEnum batchStatus;
    }

}
