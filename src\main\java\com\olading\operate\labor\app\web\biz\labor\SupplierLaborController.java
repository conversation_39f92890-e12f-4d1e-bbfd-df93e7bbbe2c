package com.olading.operate.labor.app.web.biz.labor;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.PublicController;
import com.olading.operate.labor.app.web.biz.labor.vo.CheckFailImportData;
import com.olading.operate.labor.app.web.biz.labor.vo.ImportLaborData;
import com.olading.operate.labor.app.web.biz.labor.vo.ImportLaborRow;
import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.app.web.biz.protocol.vo.SingleResponse;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.LaborQuery;
import com.olading.operate.labor.domain.service.IdentityService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SupplierLaborService;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.RedisUtils;
import com.olading.operate.labor.util.excel.ExcelIOException;
import com.olading.operate.labor.util.excel.ExcelResult;
import com.olading.operate.labor.util.excel.ExcelWriter;
import com.olading.operate.labor.util.excel.Excels;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@Tag(name = "劳务人员管理")
@RestController
@RequestMapping("/api/supplier/labor")
@RequiredArgsConstructor
@Slf4j
public class SupplierLaborController extends BusinessController {

    private final SupplierLaborService supplierLaborService;

    private final QueryService queryService;

    private final RedisUtils redisUtils;

    private final IdentityService identityService;


    private static final String BATCH_IMPORT_TEMPLATE = "/template/批量导入人员模板.xlsx";

    @Operation(summary = "劳务人员列表查询")
    @PostMapping(value = "list")
    public WebApiQueryResponse<LaborRecordVo> tempList(@RequestBody QueryFilter<LaborQueryVo> param) {

        QueryFilter<LaborQuery.Filters> filter = param.convert(LaborQueryVo::convert);
        DataSet<LaborQuery.Record> recordDataSet = queryService.queryLabor(filter);
        return WebApiQueryResponse.success(recordDataSet.getData().stream().map(LaborQuery.Record::toVo).toList(), recordDataSet.getTotal());
    }

    @Operation(summary = "人员列表统计")
    @PostMapping(value = "countLabor")
    public WebApiResponse<LaborCountVo> countLabor() {

        return WebApiQueryResponse.success(new LaborCountVo(0, 0));
    }
    @Operation(summary = "新增供应商劳务信息")
    @PostMapping(value = "create")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#param.contractId")
    })
    public WebApiResponse<SingleResponse<Long>> createSupplierLabor(@RequestBody SupplierLaborVo param) {
        Long supplier = currentSupplier().getId();
        Long laborId = supplierLaborService.createSupplierLabor(param, supplier, currentTenant());
        return WebApiResponse.success(SingleResponse.of(laborId));
    }

    @Operation(summary = "修改供应商劳务信息")
    @PostMapping(value = "update")
    public WebApiResponse<?> updateSupplierLabor(@RequestBody SupplierLaborVo param) {
        if (param.getId() == null) {
            throw new ApiException("修改的记录异常", ApiException.SYSTEM_ERROR);
        }
        supplierLaborService.updateSupplierLabor(param);
        return new WebApiResponse<>();
    }

//    @Operation(summary = "删除供应商劳务信息")
//    @PostMapping(value = "delete")
//    public WebApiResponse<?> deleteSupplierLabor(@RequestParam("id") Long id) {
//        supplierLaborService.deleteSupplierLabor(id);
//        return new WebApiResponse<>();
//    }

    @Operation(summary = "查看供应商劳务详情")
    @GetMapping(value = "get/{id}")
    public WebApiResponse<SupplierLaborVo> getSupplierLaborDetail(@PathVariable("id") Long id) {
        Long supplier = currentSupplier().getId();
        SupplierLaborVo detail = supplierLaborService.getSupplierLaborDetail(id, supplier);
        return WebApiResponse.success(detail);
    }


    @Operation(summary = "批量上传人员信息模板下载")
    @PostMapping(value = "download/template")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {

        try (Workbook hssfWorkBook = new XSSFWorkbook(new DefaultResourceLoader().getResource(BATCH_IMPORT_TEMPLATE).getInputStream())) {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("批量导入人员模板.xlsx", "UTF-8"));
            hssfWorkBook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("批量导入人员模板下载失败", e);
            throw new ApiException("批量导入人员模板下载失败", ApiException.SYSTEM_ERROR);
        }
    }

    @Operation(summary = "批量上传人员信息模板下载-校验")
    @RequestMapping(value = "download/check", method = RequestMethod.POST)
    @ResponseBody
    public WebApiResponse<UploadResult> batchUploadCheck(HttpServletRequest request, MultipartFile file) {

        Long supplierId = currentSupplier().getId();
        Map<OwnerType, Set<Long>> ownerTypeSetMap = currentDataScope();
        TenantInfo tenant = currentTenant();

        if (file.getSize() > 5 * 1024 * 1024) {
            throw new ApiException(ApiException.API_PARAM_ERROR, "文件大小不能超过5M");
        }
        ExcelResult excelResult = null;
        try {
            excelResult = Excels.reader(ImportLaborRow.class)
                    .maxDataRows(5000)
                    .needVerify(true)
                    .build()
                    .read(file.getInputStream());
        } catch (ExcelIOException e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        } catch (Exception e) {
            throw new ApiException("读取Excel文件错误", ApiException.SYSTEM_ERROR);
        }
        if (excelResult.isResultEmpty()) {
            throw new ApiException("模板中无数据,请导入数据！", ApiException.API_PARAM_ERROR);
        }
        //上传文件校验
        supplierLaborService.verifyImportLaborRow(supplierId, excelResult.getResults(), ownerTypeSetMap);

        List<List<Object>> successList = new ArrayList<>();
        List<ImportLaborRow> successDataList = new ArrayList<>();
        List<CheckFailImportData> failList = new ArrayList<>();

        excelResult.getResults().forEach(i -> {
            ImportLaborRow row = (ImportLaborRow) i;
            if (row.isVerifyFail()) {
                final CheckFailImportData bean = BeanUtil.toBean(row, CheckFailImportData.class);
                bean.setErrorMsg(row.getRowErrorString());
                failList.add(bean);
            } else {
                successDataList.add(row);
            }
        });

        if (!successDataList.isEmpty()) {
            supplierLaborService.addSupplierLabor(supplierId, successDataList, tenant);
        }

        String key = UUID.randomUUID().toString().replaceAll("-", "");
        redisUtils.set(key + "_fail", failList, 300);

        UploadResult result = new UploadResult();
        result.setSuccessCount(successDataList.size());
        result.setFailCount(failList.size());
        result.setUuid(key);
        return WebApiResponse.success(result);
    }

    @Operation(summary = "错误日志下载")
    @PostMapping(value = "/importVerifyErrorLog/{uuid}")
    public void downloadDataAuthErrorRecord(HttpServletRequest request, HttpServletResponse response, @PathVariable String uuid) throws IOException {

        List<ImportLaborData> failList = redisUtils.get(uuid + "_fail", new TypeReference<>() {
        });

        if (StringUtils.isBlank(uuid) || CollectionUtils.isEmpty(failList)) {
            throw new ApiException("文件已经失效，请重新上传文件进行下载！", ApiException.API_PARAM_ERROR);
        }


        // 创建Excel模板
        ExcelResult excelResult = Excels.createWriteResult(ImportLaborData.class);
        failList.stream().forEach(excelResult::addRow);
        // 生成并下载Excel文件
        final ExcelWriter excelWriter = Excels.writer(excelResult)
                .sheetName("批量导入模板")
                .build();
        final Workbook sheets = excelWriter.writeToWorkbook();

        String fileName = String.format("%s_%s.xlsx", StringUtils.join("批量导入人员", "_"),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        sheets.write(response.getOutputStream());
    }



    @Operation(summary = "OCR识别并验证", description = "OCR识别身份证并验证与期望信息是否匹配")
    @PostMapping("/ocrVerify")
    public WebApiResponse<OcrIdentifyResult> ocrIdentifyAndVerify(@Valid @RequestBody PublicController.OcrVerifyRequest request) {
        Long supplierId = currentSupplierId();
        log.info("OCR识别验证请求: supplierId={}, expectedName={}, expectedIdNo={}",
                supplierId, request.getExpectedName(), request.getExpectedIdNo());

        OcrIdentifyResult result = identityService.ocrIdentifyAndVerify(
                request.getPersonalIdImg(),
                request.getExpectedName(),
                request.getExpectedIdNo()
        );
        if (result.isSuccess()) {
            supplierLaborService.userVerifySuccess(supplierId, currentUserId(), result);
        }
        return WebApiResponse.success(result);
    }


    @Data
    public static class LaborQueryVo {

        @Schema(description = "姓名")
        private String name;
        @Schema(description = "手机号")
        private String cellPhone;
        @Schema(description = "证件号码")
        private String idCard;
        @Schema(description = "所属平台")
        private Long supplierId;
        @Schema(description = "所属客户")
        private Long customerId;
        @Schema(description = "所属服务合同")
        private Long contractId;
        @Schema(description = "人员状态")
        private String empStatus;

        public LaborQuery.Filters convert() {
            return Beans.copyBean(this, LaborQuery.Filters.class);
        }
    }

    @Data
    public static class LaborRecordVo {

        @Schema(description = "姓名")
        private String name;

        @Schema(description = "id")
        private Long id;

        @Schema(description = "证件号码")
        private String idCard;

        @Schema(description = "手机号")
        private String cellphone;

        @Schema(description = "签约状态")
        private Boolean signStatus;

        @Schema(description = "所属客户")
        private String customerName;

        @Schema(description = "所属作业主体")
        private String corporationName;

        @Schema(description = "人员状态 ON_THE_JOB:在职，QUIT:离职")
        private String empStatus;
        @Schema(description = "创建时间")
        private LocalDateTime createTime;
        @Schema(description = "更新时间")
        private LocalDateTime modifyTime;
        @Schema(description = "平台名")
        private String supplierName;
        @Schema(description = "服务合同名")
        private String contractName;

    }


    @Getter
    @Setter
    public static class UploadResult {
        @Schema(description = "成功条数")
        private Integer successCount;
        @Schema(description = "失败条数")
        private Integer failCount;
        @Schema(description = "key")
        private String uuid;
    }

    @Getter
    @Setter
    public static class LaborCountVo {
        @Schema(description = "在职人数")
        private Integer onTheJobCount;
        @Schema(description = "离职人数")
        private Integer failCount;

        public LaborCountVo(Integer onTheJobCount, Integer failCount) {
            this.onTheJobCount = onTheJobCount;
            this.failCount = failCount;
        }
    }



    @Schema(description=("劳务人员查询接口"))
    @Getter
    @Setter
    public static class SupplierLaborQueryVo {
        @Schema(description = "姓名")
        private String name;
        @Schema(description = "手机号")
        private String cellPhone;
        @Schema(description = "证件号码")
        private String idCard;
        @Schema(description = "人员状态")
        private String empStatus;
        @Schema(description = "所属平台")
        private Long supplierId;
        @Schema(description = "所属客户")
        private Long customerId;
        @Schema(description = "所属服务合同")
        private Long contractId;
    }
}