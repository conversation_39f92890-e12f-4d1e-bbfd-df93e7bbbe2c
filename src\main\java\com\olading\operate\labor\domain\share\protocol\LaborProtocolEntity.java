package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.LocalDate;

@Getter
@Setter
@Comment("劳务人员协议表")
@Entity
@Table(name = "t_labor_protocol")
@AttributeOverrides({
        @AttributeOverride(name = "version", column = @Column(name = "version"))
})
public class LaborProtocolEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "id_card")
    private String idCard;

    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id")
    private Long supplierCorporationId;

    @Size(max = 20)
    @Comment("协议状态")
    @Column(name = "protocol_status", length = 20)
    private String protocolStatus;

    @Comment("协议开始日期")
    @Column(name = "start_date")
    private LocalDate startDate;

    @Comment("协议截止日期")
    @Column(name = "end_date")
    private LocalDate endDate;

    public static LaborProtocolEntity create(SupplierLaborEntity labor, Long supplierId, Long supplierCorporationId,
                                             String protocolStatus, LocalDate startDate, LocalDate endDate) {
        LaborProtocolEntity entity = new LaborProtocolEntity();
        entity.name = labor.getName();
        entity.idCard = labor.getIdCard();
        entity.supplierId = supplierId;
        entity.supplierCorporationId = supplierCorporationId;
        entity.protocolStatus = protocolStatus;
        entity.startDate = startDate;
        entity.endDate = endDate;
        entity.setProtocolStatus("CREATED");//TODO 待完善
        return entity;
        }

}