package com.olading.operate.labor.domain.supplier;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.operate.labor.domain.share.customer.CustomerData;
import lombok.Data;

import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class SupplierCustomerData {

    private Long id;

    private String customerNo;

    private Long customerId;

    private LocalDateTime bizCreateTime;

    private CustomerData customer = new CustomerData();


    public SupplierCustomerData(SupplierCustomerEntity entity) {
        this.id = entity.getId();
        this.customerId = entity.getCustomerId();
        this.customerNo = entity.getCustomerNo();
        this.bizCreateTime = entity.getBizCreateTime();
    }

    public SupplierCustomerData() {
    }
}
