package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.invoice.InvoiceManager;
import com.olading.operate.labor.domain.invoice.InvoiceStatus;
import com.olading.operate.labor.domain.invoice.dto.AvailableBillRequest;
import com.olading.operate.labor.domain.invoice.dto.InvoiceCreateRequest;
import com.olading.operate.labor.domain.invoice.dto.InvoicePresetRequest;
import com.olading.operate.labor.domain.invoice.vo.AvailableBillVO;
import com.olading.operate.labor.domain.invoice.vo.InvoicePresetVO;
import com.olading.operate.labor.domain.invoice.vo.InvoiceVO;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * 开票服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class InvoiceService {

    private final InvoiceManager invoiceManager;
    private final BusinessContractManager contractManager;
    private final FileManager fileManager;

    /**
     * 获取开票预设信息
     */
    public InvoicePresetVO getInvoicePresetInfo(Long supplierId, InvoicePresetRequest request) {
        log.info("获取开票预设信息: supplierId={}, contractId={}, billMonth={}",
                supplierId, request.getContractId(), request.getBillMonth());

        // 验证合同权限
        validateContractPermission(supplierId, request.getContractId());

        return invoiceManager.getInvoicePresetInfo(supplierId, request.getContractId(), handleMonth(request.getBillMonth()));
    }

    /**
     * 获取可开票账单列表
     */
    public List<AvailableBillVO> getAvailableBills(Long supplierId, AvailableBillRequest request) {
        log.info("获取可开票账单列表: supplierId={}, contractId={}, billMonth={}", 
                supplierId, request.getContractId(), request.getBillMonth());
        
        // 验证合同权限
        validateContractPermission(supplierId, request.getContractId());
        
        return invoiceManager.getAvailableBills(supplierId, request.getContractId(), handleMonth(request.getBillMonth()));
    }

    /**
     * 创建开票申请
     */
    @Transactional
    public InvoiceVO createInvoiceApplication(Long supplierId, InvoiceCreateRequest request) {
        log.info("创建开票申请: supplierId={}, contractId={}, totalFee={}", 
                supplierId, request.getContractId(), 
                request.getItems().stream().map(item -> item.getFee()).reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add));
        
        // 验证合同权限
        validateContractPermission(supplierId, request.getContractId());
        
        return invoiceManager.createInvoiceApplication(supplierId, request);
    }

    /**
     * 获取开票申请详情
     */
    public InvoiceVO getInvoiceDetail(Long supplierId, Long invoiceId) {
        log.info("获取开票申请详情: supplierId={}, invoiceId={}", supplierId, invoiceId);
        
        return invoiceManager.getInvoiceDetail(supplierId, invoiceId);
    }

    /**
     * 上传发票文件
     */
    @Transactional(rollbackFor = Exception.class)
    public void uploadInvoiceFile(Long supplierId, Long invoiceId, MultipartFile file) throws IOException {
        log.info("上传发票文件: supplierId={}, invoiceId={}, fileName={}", 
                supplierId, invoiceId, file.getOriginalFilename());

        final String fileId = fileManager.save(file.getOriginalFilename(),
                file.getInputStream(), LocalDateTime.now().plusYears(100),
                OwnerType.SUPPLIER, String.valueOf(supplierId));
        
        // 更新开票状态为已开票
        invoiceManager.updateInvoiceStatus(supplierId, invoiceId, InvoiceStatus.INVOICED, null, fileId);
    }

    /**
     * 退回开票申请
     */
    @Transactional(rollbackFor = Exception.class)
    public void returnInvoiceApplication(Long supplierId, Long invoiceId, String reason) {
        log.info("退回开票申请: supplierId={}, invoiceId={}, reason={}", supplierId, invoiceId, reason);
        
        invoiceManager.updateInvoiceStatus(supplierId, invoiceId, InvoiceStatus.RETURNED, reason, null);
    }

    /**
     * 验证合同权限
     */
    private void validateContractPermission(Long supplierId, Long contractId) {
        ContractVo contract = contractManager.queryContract(contractId);
        if (contract == null || !contract.getSupplierId().equals(supplierId)) {
            throw new SecurityException("无权限访问该合同");
        }
    }


    /**
     * 处理月份，默认为当前月份，2023-05-04 -> 2023-05-01
     */
    private static LocalDate handleMonth(LocalDate month) {
        if (month == null){
            return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        }
        return month.with(TemporalAdjusters.firstDayOfMonth());
    }
}