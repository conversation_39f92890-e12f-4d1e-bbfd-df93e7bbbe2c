package com.olading.operate.labor.app.web.biz.bill;

import cn.hutool.core.bean.BeanUtil;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.boot.util.validate.FileSize;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.bill.BillMasterStatus;
import com.olading.operate.labor.domain.bill.dto.BillGenerateRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportConfirmRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportPreviewResult;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportResult;
import com.olading.operate.labor.domain.bill.vo.*;
import com.olading.operate.labor.domain.query.BillQuery;
import com.olading.operate.labor.domain.query.BillSalaryDetailQuery;
import com.olading.operate.labor.domain.query.BillManagementFeeDetailQuery;
import com.olading.operate.labor.domain.query.BillOtherFeeDetailQuery;
import com.olading.operate.labor.domain.service.BillService;
import com.olading.operate.labor.domain.service.QueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 账单管理控制器 - 版本
 */
@RestController
@RequestMapping("/api/supplier/bills")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "账单管理", description = "账单生成、查询、确认等功能")
public class BillController extends BusinessController {

    private final BillService billService;
    private final QueryService queryService;

    @Operation(summary = "生成账单", description = "根据合同ID和账单月份生成账单，系统自动获取供应商、客户、作业主体等信息")
    @PostMapping("/generate")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#request.contractId")
    })
    public WebApiResponse<BillMasterVO> generateBill(@Valid @RequestBody BillGenerateRequest request) {
        Long supplierId = currentSupplierId();
        log.info("生成账单请求: {}, 当前供应商: {}", request, supplierId);

        BillMasterVO bill = billService.generateBill(currentTenant(), supplierId, request);

        log.info("账单生成成功: billId={}, billNo={}, contractId={}, supplierId={}",
                bill.getId(), bill.getBillNo(), request.getContractId(), supplierId);

        return WebApiResponse.success(bill);
    }

    @Operation(summary = "提交账单确认")
    @PostMapping("/submit")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    public WebApiResponse<Void> submitBillForConfirm(@Valid @RequestBody IdRequest  request) {
        Long supplierId = currentSupplierId();
        log.info("提交账单确认请求, supplierId: {}, billId: {}", supplierId, request.getBillId());
        
        // 在操作前校验权限
        billService.validateBillPermissionWithDataScope(supplierId,  request.getBillId(), currentDataScope(),isAdmin());
        billService.submitBillForConfirm(supplierId,  request.getBillId());
        return WebApiResponse.success();
    }

    @Operation(summary = "确认账单")
    @PostMapping("/confirm")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    public WebApiResponse<Void> confirmBill(@Valid @RequestBody IdRequest request) {
        Long supplierId = currentSupplierId();
        log.info("确认账单请求, supplierId: {}, billId: {}", supplierId, request.getBillId());

        // 在操作前校验权限
        billService.validateBillPermissionWithDataScope(supplierId, request.getBillId(), currentDataScope(),isAdmin());
        billService.confirmBill(supplierId, request.getBillId());

        return WebApiResponse.success();
    }

    @Operation(summary = "删除账单")
    @PostMapping("/delete")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    public WebApiResponse<Void> deleteBill(@Valid @RequestBody IdRequest request) {
        Long supplierId = currentSupplierId();
        log.info("删除账单请求, supplierId: {}, billId: {}", supplierId, request.getBillId());

        // 在操作前校验权限
        billService.validateBillPermissionWithDataScope(supplierId, request.getBillId(), currentDataScope(),isAdmin());
        billService.deleteBill(supplierId, request.getBillId());

        return WebApiResponse.success();
    }

    @Operation(summary = "获取账单详情")
    @PostMapping("/detail")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    public WebApiResponse<BillMasterVO> getBillDetail(@Valid @RequestBody IdRequest request) {
        Long supplierId = currentSupplierId();
        log.info("获取账单详情请求, supplierId: {}, billId: {}", supplierId, request.getBillId());

        // 在操作前校验权限
        billService.validateBillPermissionWithDataScope(supplierId, request.getBillId(), currentDataScope(),isAdmin());
        BillMasterVO bill = billService.getBillDetail(supplierId, request.getBillId());

        return WebApiResponse.success(bill);
    }

    @Operation(summary = "分页查询账单列表")
    @PostMapping("/list")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<BillMasterVO> queryBills(@RequestBody QueryFilter<WebBillFilters> request) {
        log.info("分页查询账单列表请求: {}", request);

        QueryFilter<BillQuery.Filters> filter = request.convert(WebBillFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        DataSet<BillMasterVO> ds = queryService.queryBill(filter);

        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "获取账单薪酬明细")
    @PostMapping("/salary-details")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<BillSalaryDetailVO> getBillSalaryDetails(@RequestBody @Valid QueryFilter<WebBillSalaryDetailFilters> request) {
        log.info("获取账单薪酬明细请求: {}", request);

        QueryFilter<BillSalaryDetailQuery.Filters> filter = request.convert(WebBillSalaryDetailFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        DataSet<BillSalaryDetailVO> ds = queryService.queryBillSalaryDetail(filter);

        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "获取账单管理费明细")
    @PostMapping("/management-fee-details")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<BillManagementFeeDetailVO> getBillManagementFeeDetails(@RequestBody QueryFilter<WebBillManagementFeeDetailFilters> request) {
        log.info("获取账单管理费明细请求: {}", request);

        QueryFilter<BillManagementFeeDetailQuery.Filters> filter = request.convert(WebBillManagementFeeDetailFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        DataSet<BillManagementFeeDetailVO> ds = queryService.queryBillManagementFeeDetail(filter);

        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "获取账单其他费用明细")
    @PostMapping("/other-fee-details")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<BillOtherFeeDetailVO> getBillOtherFeeDetails(@RequestBody QueryFilter<WebBillOtherFeeDetailFilters> request) {
        log.info("获取账单其他费用明细请求: {}", request);

        QueryFilter<BillOtherFeeDetailQuery.Filters> filter = request.convert(WebBillOtherFeeDetailFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        DataSet<BillOtherFeeDetailVO> ds = queryService.queryBillOtherFeeDetail(filter);

        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "下载其他费用导入模板", description = "下载包含示例数据的Excel导入模板")
    @GetMapping("/other-fees/import/template")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    public void downloadOtherFeeImportTemplate(HttpServletResponse response) throws IOException {
        Long supplierId = currentSupplierId();
        log.info("下载其他费用导入模板请求, supplierId: {}", supplierId);
        billService.downloadOtherFeeImportTemplate(supplierId, response);
    }

    @Operation(summary = "导入其他费用数据", description = "上传Excel文件并预览导入数据，进行数据验证")
    @PostMapping(value = "/other-fees/import/preview", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    public WebApiResponse<BillOtherFeeImportResult> previewOtherFeeImport(
            @Valid @ModelAttribute BillController.UploadBillFileRequest request) {
        Long supplierId = currentSupplierId();
        log.info("导入其他费用数据请求, supplierId: {}, billId: {}", supplierId, request.getBillId());
        
        // 在操作前校验权限
        billService.validateBillPermissionWithDataScope(supplierId, request.getBillId(), currentDataScope(),isAdmin());
        BillOtherFeeImportPreviewResult result = billService.previewOtherFeeImport(supplierId, request.getBillId(), request.file);
        if(result.isSuccess()){
            log.info("预览导入其他费用数据成功, supplierId: {}, billId: {}", supplierId, request.getBillId());
            BillOtherFeeImportConfirmRequest cRequest = new BillOtherFeeImportConfirmRequest();
            cRequest.setImportData(result.getPreviewData());
            BillOtherFeeImportResult importResult = billService.confirmOtherFeeImport(supplierId, request.getBillId(), cRequest);
            return WebApiResponse.success(importResult);
        }else{
            final BillOtherFeeImportResult bean = BeanUtil.toBean(result, BillOtherFeeImportResult.class);
            bean.setSuccess(false);
            bean.setMessage(result.getErrors().stream().map(s->"行号:"+s.getRowNum()+" 错误信息:"+s.getErrorMessage()).collect(Collectors.joining(";")));
            return WebApiResponse.success(bean);
        }

    }

    @Data
    @Schema(description = "上传其他费用")
    public static class UploadBillFileRequest {

        @NotNull(message = "账单Id不为空")
        @Schema(description = "账单ID")
        private Long billId;

        @FileSize("20M")
        private MultipartFile file;
    }

    @Data
    public static class BillIdRequest {
        @NotNull(message = "账单Id不为空")
        @Schema(description = "账单ID")
        private Long billId;
    }

    @Data
    public static class WebBillFilters {

        @Schema(description = "账单ID")
        private Long id;

        @Schema(description = "供应商ID")
        private Long supplierId;

        @Schema(description = "客户ID")
        private Long customerId;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "合同ID")
        private Long contractId;

        @Schema(description = "账单编号")
        private String billNo;

        @Schema(description = "账单月份开始")
        private LocalDate billMonthStart;

        @Schema(description = "账单月份结束")
        private LocalDate billMonthEnd;

        @Schema(description = "账单状态")
        private BillMasterStatus billStatus;

        @Schema(description = "客户名称")
        private String customerName;

        @Schema(description = "合同名称")
        private String contractName;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        @Schema(description = "客户列表")
        private Set<Long> customerIds;

        public BillQuery.Filters convert() {
            return BeanUtil.toBean(this, BillQuery.Filters.class);
        }
    }

    @Data
    public static class IdRequest{
        @NotNull(message = "账单Id不能为空")
        @Schema(description = "账单Id")
        private Long billId;
    }

    @Data
    public static class WebBillSalaryDetailFilters {

        @Schema(description = "明细ID")
        private Long id;

        @Schema(description = "账单主表ID", required = true)
        private Long billMasterId;

        @Schema(description = "账单分类ID")
        private Long billCategoryId;

        @Schema(description = "薪酬明细ID")
        private Long salaryDetailId;

        @Schema(description = "薪酬批次ID")
        private Long salaryBatchId;

        @Schema(description = "人员姓名")
        private String laborName;

        @Schema(description = "身份证号")
        private String idCard;

        @Schema(description = "账单月份")
        private LocalDate billMonth;

        @Schema(description = "账单月份开始")
        private LocalDate billMonthStart;

        @Schema(description = "账单月份结束")
        private LocalDate billMonthEnd;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        public BillSalaryDetailQuery.Filters convert() {
            return BeanUtil.toBean(this, BillSalaryDetailQuery.Filters.class);
        }
    }

    @Data
    public static class WebBillManagementFeeDetailFilters {

        @Schema(description = "明细ID")
        private Long id;

        @Schema(description = "账单主表ID", required = true)
        private Long billMasterId;

        @Schema(description = "账单分类ID")
        private Long billCategoryId;

        @Schema(description = "人员姓名")
        private String laborName;

        @Schema(description = "身份证号")
        private String idCard;

        @Schema(description = "收费项目")
        private String feeItem;

        @Schema(description = "账单月份")
        private LocalDate billMonth;

        @Schema(description = "账单月份开始")
        private LocalDate billMonthStart;

        @Schema(description = "账单月份结束")
        private LocalDate billMonthEnd;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        public BillManagementFeeDetailQuery.Filters convert() {
            return BeanUtil.toBean(this, BillManagementFeeDetailQuery.Filters.class);
        }
    }

    @Data
    public static class WebBillOtherFeeDetailFilters {

        @Schema(description = "明细ID")
        private Long id;

        @Schema(description = "账单主表ID", required = true)
        private Long billMasterId;

        @Schema(description = "账单分类ID")
        private Long billCategoryId;

        @Schema(description = "人员姓名")
        private String laborName;

        @Schema(description = "身份证号")
        private String idCard;

        @Schema(description = "费用名称")
        private String feeName;

        @Schema(description = "费用类别")
        private String feeCategory;

        @Schema(description = "导入批次号")
        private String importBatchNo;

        @Schema(description = "账单月份")
        private LocalDate billMonth;

        @Schema(description = "账单月份开始")
        private LocalDate billMonthStart;

        @Schema(description = "账单月份结束")
        private LocalDate billMonthEnd;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        public BillOtherFeeDetailQuery.Filters convert() {
            return BeanUtil.toBean(this, BillOtherFeeDetailQuery.Filters.class);
        }
    }
}