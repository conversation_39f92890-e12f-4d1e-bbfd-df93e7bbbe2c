package com.olading.operate.labor.domain.proxy;

import com.olading.operate.labor.domain.proxy.order.ProxyOrderEntity;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderStatusEnum;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProxyOrderData {


    private Long id;


    @Schema(description = "灵工平台id")
    private Long supplierId;


    @Schema(description = "客户id")
    private Long customerId;


    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;


    @Schema(description = "合同id")
    private Long contractId;

    @Schema(description = "代发批次id")
    private Long proxyBatchId;

    @Schema(description = "应发工资id")
    private Long salaryDetailId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号")
    private String idCard;


    @Schema(description = "手机号")
    private String cellphone;

    @Schema(description = "银行卡号")
    private String bankCard;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "通道编码")
    private String payChannel;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "错误码")
    private String errorCode;

    @Schema(description = "错误信息")
    private String lastErrorInfo;

    @Schema(description = "状态 CHECK_SUCC/CHECK_FAIL/PROCESSING/REMIT/FAIL")
    private ProxyOrderStatusEnum status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "应发金额")
    private BigDecimal payableAmount;

    @Schema(description = "本次应预扣预缴税额")
    private BigDecimal currentTaxWithholding;

    @Schema(description = "增值税额")
    private BigDecimal vatAmount;

    @Schema(description = "增值附加税额")
    private BigDecimal additionalTaxAmount;

    @Schema(description = "城市维护建设附加税")
    private BigDecimal urbanConstructionTax;

    @Schema(description = "教育费附加税")
    private BigDecimal educationSurcharge;

    @Schema(description = "地方教育附加税")
    private BigDecimal localEducationSurcharge;

    public static ProxyOrderData of(ProxyOrderEntity proxyOrder, SalaryDetailEntity salaryDetailEntity) {
        ProxyOrderData data = new ProxyOrderData();
        if(proxyOrder == null) return data;
        data.setId(proxyOrder.getId());
        data.setSupplierId(proxyOrder.getSupplierId());
        data.setCustomerId(proxyOrder.getCustomerId());
        data.setSupplierCorporationId(proxyOrder.getSupplierCorporationId());
        data.setContractId(proxyOrder.getContractId());
        data.setProxyBatchId(proxyOrder.getProxyBatchId());
        data.setSalaryDetailId(proxyOrder.getSalaryDetailId());
        data.setName(proxyOrder.getName());
        data.setIdCard(proxyOrder.getIdCard());
        data.setCellphone(proxyOrder.getCellphone());
        data.setBankCard(proxyOrder.getBankCard());
        data.setAmount(proxyOrder.getAmount());
        data.setPayChannel(proxyOrder.getPayChannel());
        data.setCompleteTime(proxyOrder.getCompleteTime());
        data.setErrorCode(proxyOrder.getErrorCode());
        data.setLastErrorInfo(proxyOrder.getLastErrorInfo());
        data.setStatus(proxyOrder.getStatus());
        data.setRemark(proxyOrder.getRemark());
        data.setVatAmount(salaryDetailEntity.getVatAmount());
        data.setAdditionalTaxAmount(salaryDetailEntity.getAdditionalTaxAmount());
        data.setPayableAmount(salaryDetailEntity.getPayableAmount());
        data.setUrbanConstructionTax(salaryDetailEntity.getUrbanConstructionTax());
        data.setEducationSurcharge(salaryDetailEntity.getEducationSurcharge());
        data.setLocalEducationSurcharge(salaryDetailEntity.getLocalEducationSurcharge());
        return data;
    }



}
