package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.proxy.ProxyOrderData;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderEntity;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderStatusEnum;
import com.olading.operate.labor.domain.proxy.order.QProxyOrderEntity;
import com.olading.operate.labor.domain.salary.QSalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.supplier.QSupplierEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class ProxyOrderQuery implements EntityQuery<QueryFilter<ProxyOrderQuery.Filters>, ProxyOrderData> {

    private final QProxyOrderEntity t1 = QProxyOrderEntity.proxyOrderEntity;
    private final QSalaryDetailEntity t2 = QSalaryDetailEntity.salaryDetailEntity;
    private final QSupplierEntity t3 = QSupplierEntity.supplierEntity;
    private final QSupplierCorporationEntity t4 = QSupplierCorporationEntity.supplierCorporationEntity;
    private final QBusinessContractEntity t5 = QBusinessContractEntity.businessContractEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.in(filters.getFilters().getId()));
        }
        if (filters.getFilters().getSupplierId() != null){
            criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getCorporation())) {
            criteria.and(t4.name.like(JpaUtils.fullLike(filters.getFilters().getCorporation())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getBusinessContract())) {
            criteria.and(t5.name.like(JpaUtils.fullLike(filters.getFilters().getBusinessContract())));
        }
        if (filters.getFilters().getBatchId() != null) {
            criteria.and(t1.proxyBatchId.eq(filters.getFilters().getBatchId()));
        }
        if (filters.getFilters().getSalaryDetailId() != null) {
            criteria.and(t1.salaryDetailId.eq(filters.getFilters().getSalaryDetailId()));
        }
        if (filters.getFilters().getStatus() != null) {
            criteria.and(t1.status.eq(filters.getFilters().getStatus()));
        }
        if (filters.getFilters().getCorporationIds() != null){
            criteria.and(t1.supplierCorporationId.in(filters.getFilters().getCorporationIds()));
        }
        if (filters.getFilters().getContractIds() != null){
            criteria.and(t1.contractId.in(filters.getFilters().getContractIds()));
        }

        query.select(t1, t2, t3, t4, t5)
                .from(t1)
                .leftJoin(t2).on(t1.salaryDetailId.eq(t2.id))
                .leftJoin(t3).on(t1.supplierId.eq(t3.id))
                .leftJoin(t4).on(t1.supplierCorporationId.eq(t4.id))
                .leftJoin(t5).on(t1.contractId.eq(t5.id));
        query.where(criteria);
    }

    @Override
    public ProxyOrderData transform(Object v) {
        Tuple tuple = (Tuple) v;
        ProxyOrderEntity proxyOrder = tuple.get(t1);
        SalaryDetailEntity salaryDetailEntity = tuple.get(t2);
        return ProxyOrderData.of(proxyOrder, salaryDetailEntity);
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    @Builder
    public static class Filters {

        private Long id;

        private Long supplierId;

        private String corporation;

        private Long batchId;

        private Long salaryDetailId;

        private String businessContract;

        private List<Long> corporationIds;

        private List<Long> contractIds;

        private ProxyOrderStatusEnum status;
    }

}
