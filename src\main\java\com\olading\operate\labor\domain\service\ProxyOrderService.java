package com.olading.operate.labor.domain.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.json.JSONUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelEntity;
import com.olading.operate.labor.domain.proxy.ChannelRemitProvider;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitOrderEntity;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitRequest;
import com.olading.operate.labor.domain.proxy.channel.RemitStatusEnum;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchEntity;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchStatusEnum;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderEntity;
import com.olading.operate.labor.domain.proxy.ProxyOrderManager;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderStatusEnum;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.share.identity.IdentifyProvider;
import com.olading.operate.labor.domain.share.identity.PersonVo;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.LockModeType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class ProxyOrderService {


    private final ProxyOrderManager proxyOrderManager;
    private final SalaryManager salaryManager;
    private final CorporationManager corporationManager;
    private final LaborInfoRepository laborInfoRepository;
    private final IdentifyProvider identifyProvider;
    private final ChannelRemitProvider channelRemitProvider;


    /**
     * 根据工资批次创建代付批次
     * @param statementId
     * @return
     */
    public Long createBatch(Long statementId,String remark){
        log.info("创建代付批次{},{}",statementId,remark);
        //查询工资批次表是否待确认
        final SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(statementId);
        if(salaryStatementEntity.getStatus() != SalaryStatementStatus.CONFIRMED){
            throw new BusinessException("工资批次未确认");
        }
        //是否存在对应的未完成的代发批次
        ProxyBatchEntity batch = proxyOrderManager.queryUnCompleteBatchByStatementId(statementId);
        if(batch != null){
            throw new BusinessException("存在待完成的代发批次");
        }
        //查询是否存在工资明细
        List<SalaryDetailEntity> salaryDetailEntityList = salaryManager.querySalaryDetailByStatementId(statementId);
        if(CollectionUtil.isEmpty(salaryDetailEntityList)){
            throw new BusinessException("无工资明细");
        }

        //查询默认出款通道
        final CorporationPayChannelEntity corporationPayChannelEntity = corporationManager.queryDefaultPayChannel(salaryStatementEntity.getSupplierCorporationId());
        if (corporationPayChannelEntity == null){
            throw new BusinessException("未配置默认出款通道");
        }

        //筛选出待代付的工资明细（未进行过代发，或者存在校验失败、出款失败、退款）
        List<SalaryDetailEntity>  salaryDetail =  proxyOrderManager.queryCanProxySalaryDetail(statementId);
        if(CollectionUtil.isEmpty(salaryDetail)){
            throw new BusinessException("无待代付的工资明细");
        }

        //创建代付批次
        Pair<ProxyBatchEntity, List<ProxyOrderEntity>> pair = proxyOrderManager.createBatch(salaryStatementEntity, salaryDetail, corporationPayChannelEntity, remark);

        //进行代付检查并回填手机号和银行卡号
        limitCheck(pair.getValue());

        log.info("创建代付批次结束{},{}",statementId,remark);

        return pair.getKey().getId();

    }

    public void limitCheck(List<ProxyOrderEntity> list){
        list.forEach(order -> {
            try {
                proxyOrderLimitCheck(order.getId());
            } catch (Exception e) {
                log.info("代付检查异常,{},{}",e.getMessage(),JSONUtil.toJsonStr(order));
                order.setLastErrorInfo("核验失败");
                order.setStatus(ProxyOrderStatusEnum.CHECK_FAIL);
                proxyOrderManager.saveOrder(order);
            }
        });
    }

    private void proxyOrderLimitCheck(Long orderId) {
        log.info("开始进行代付检查,orderId={}", orderId);

        // 加悲观写锁确保并发安全
        ProxyOrderEntity proxyOrder = proxyOrderManager.findProxyOrder(orderId, LockModeType.PESSIMISTIC_WRITE);

        // 查询劳务信息
        final SupplierLaborVo laborVo = laborInfoRepository.findLaborInfoByContract(
                proxyOrder.getContractId(),
                proxyOrder.getIdCard()
        );

        if (laborVo == null) {
            proxyOrder.setLastErrorInfo("未查询到劳务人员相关信息");
            proxyOrder.setStatus(ProxyOrderStatusEnum.CHECK_FAIL);
            proxyOrderManager.saveOrder(proxyOrder);
            return;
        }

        proxyOrder.setCellphone(laborVo.getCellphone());
        proxyOrder.setBankCard(laborVo.getBankCard());

        if (StringUtils.isBlank(laborVo.getCellphone()) || StringUtils.isBlank(laborVo.getBankCard())) {
            proxyOrder.setLastErrorInfo("未查询到劳务人员银行卡或手机号");
            proxyOrder.setStatus(ProxyOrderStatusEnum.CHECK_FAIL);
            proxyOrderManager.saveOrder(proxyOrder);
            return;
        }

        // 三要素鉴权
        PersonVo personVo = PersonVo.builder()
                .idCardNo(proxyOrder.getIdCard())
                .realName(proxyOrder.getName())
                .bankCardNo(proxyOrder.getBankCard())
                .cellphoneNo(proxyOrder.getCellphone())
                .build();

        String msg = identifyProvider.authLimit3(personVo);

        if (StringUtils.isNotBlank(msg)) {
            log.warn("三要素鉴权失败,orderId={},msg={}", orderId, msg);
            proxyOrder.setLastErrorInfo(msg);
            proxyOrder.setStatus(ProxyOrderStatusEnum.CHECK_FAIL);
            proxyOrderManager.saveOrder(proxyOrder);
            return;
        }

        // 成功
        proxyOrder.setStatus(ProxyOrderStatusEnum.CHECK_SUCC);
        proxyOrderManager.saveOrder(proxyOrder);
        log.info("代付检查成功,orderId={}", orderId);
    }


    /**
     * 删除代付批次
     */
    public void removeBatch(Long batchId){
        ProxyBatchEntity proxyBatchEntity = proxyOrderManager.requiredProxyBatch(batchId);
        if(proxyBatchEntity.getBatchStatus() != ProxyBatchStatusEnum.CHECK){
            throw new BusinessException("代付批次状态不正确,不能删除！");
        }
        List<ProxyOrderEntity> proxyOrderByBatchId = proxyOrderManager.findProxyOrderByBatchId(batchId);
        if(proxyOrderByBatchId != null){
            Optional<ProxyOrderEntity> any = proxyOrderByBatchId.stream().filter(order -> order.getStatus() == ProxyOrderStatusEnum.PROCESSING
                    || order.getStatus() == ProxyOrderStatusEnum.FAIL || order.getStatus() == ProxyOrderStatusEnum.REFUND).findAny();
            if(any.isPresent()){
                throw new BusinessException("代付批次下有进行中或已完成的订单,不能删除！");
            }
        }
        proxyOrderManager.remove(batchId);
    }


    /**
     * 确认代付批次
     * @param batchId
     */
    public void confirmBatch(Long batchId){
        ProxyBatchEntity proxyBatchEntity = proxyOrderManager.requiredProxyBatch(batchId);
        if(proxyBatchEntity.getBatchStatus() != ProxyBatchStatusEnum.CHECK){
            throw new BusinessException("代付批次状态不正确,不能支付！");
        }
        List<ProxyOrderEntity> orders = proxyOrderManager.findProxyOrderByBatchId(batchId);
        orders.forEach(order -> {
            if(order.getStatus() != ProxyOrderStatusEnum.CHECK_SUCC){
                throw new BusinessException("代付批次下有未通过核验的订单,不能支付！");
            }
        });

        // 变更批次为支付中
        proxyOrderManager.processBatch(proxyBatchEntity,orders);

        // 进行代付
        List<ProxyOrderEntity> payOrdder = orders.stream().filter(order -> order.getStatus() == ProxyOrderStatusEnum.PROCESSING).collect(Collectors.toList());
        payOrdder.forEach(order -> {
            channelRemitProvider.processRemit(ChannelRemitRequest.of(order));
        });

    }


    public void queryRemitResult(){
        final List<ChannelRemitOrderEntity> entityList = proxyOrderManager.queryProcessing(LocalDateTime.now().minusDays(15),LocalDateTime.now().minusSeconds(60));
        entityList.forEach(entity -> {
            channelRemitProvider.queryRemitResult(entity.getId());
        });
    }

}
