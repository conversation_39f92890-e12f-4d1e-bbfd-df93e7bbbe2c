package com.olading.operate.labor.domain.supplier;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 绑定到服务运营方的客户
 */
@Table(name = "t_supplier_customer", indexes = {
        @Index(name = "i_supplier_customer_1", columnList = "supplier_id, customer_no", unique = true)
})
@Entity
public class SupplierCustomerEntity extends BaseEntity {

    @Comment("业务创建时间")
    @Column(name = "biz_create_time")
    protected LocalDateTime bizCreateTime;
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;
    @NotNull
    @Comment("服务运营方编号")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;
    @NotNull
    @Comment("客户ID")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;
    @Size(max = 100)
    @NotEmpty
    @Comment("客户编号")
    @Column(name = "customer_no", nullable = false, length = 100)
    private String customerNo;


    public SupplierCustomerEntity(long supplierId, long customerId, String customerNo) {
        this.supplierId = supplierId;
        this.customerId = customerId;
        this.customerNo = customerNo;
    }

    protected SupplierCustomerEntity() {
    }

    public Long getId() {
        return id;
    }

    public long getSupplierId() {
        return supplierId;
    }

    public long getCustomerId() {
        return customerId;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public LocalDateTime getBizCreateTime() {
        return bizCreateTime;
    }

    void setBizCreateTime(LocalDateTime bizCreateTime) {
        this.bizCreateTime = bizCreateTime;
    }
}
