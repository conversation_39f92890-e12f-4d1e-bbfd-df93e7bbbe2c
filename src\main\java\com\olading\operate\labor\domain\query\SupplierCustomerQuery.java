package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.customer.CustomerData;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.QEnterpriseInfoEntity;
import com.olading.operate.labor.domain.supplier.QSupplierCustomerEntity;
import com.olading.operate.labor.domain.supplier.SupplierCustomerData;
import com.olading.operate.labor.domain.supplier.SupplierCustomerEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

public class SupplierCustomerQuery implements EntityQuery<QueryFilter<SupplierCustomerQuery.Filters>, SupplierCustomerData> {

    private final QCustomerEntity t1 = QCustomerEntity.customerEntity;
    private final QEnterpriseInfoEntity t2 = QEnterpriseInfoEntity.enterpriseInfoEntity;
    private final QSupplierCustomerEntity t3 = QSupplierCustomerEntity.supplierCustomerEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t3.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        if (CollectionUtils.isNotEmpty(filters.getFilters().getId())) {
            criteria.and(t1.id.in(filters.getFilters().getId()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getName())) {
            criteria.and(t1.name.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }
        /*if (filters.getFilters().getCreateTimeBegin() != null) {
            criteria.and(t1.createTime.goe(filters.getFilters().getCreateTimeBegin()));
        }
        if (filters.getFilters().getCreateTimeEnd() != null) {
            criteria.and(t1.createTime.lt(filters.getFilters().getCreateTimeEnd()));
        }*/
        if (filters.getFilters().getBizCreateTimeBegin() != null) {
            criteria.and(t3.bizCreateTime.goe(filters.getFilters().getBizCreateTimeBegin()));
        }
        if (filters.getFilters().getBizCreateTimeEnd() != null) {
            criteria.and(t3.bizCreateTime.lt(filters.getFilters().getBizCreateTimeEnd()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getContacts())) {
            criteria.and(t2.contacts.like(JpaUtils.fullLike(filters.getFilters().getContacts())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getContactPhone())) {
            criteria.and(t2.contactPhone.like(JpaUtils.fullLike(filters.getFilters().getContactPhone())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getCustomerNo())) {
            criteria.and(t3.customerNo.eq(filters.getFilters().getCustomerNo()));
        }


        query.select(t3, t1, t2)
                .from(t1)
                .innerJoin(t2).on(t1.enterpriseInfoId.eq(t2.id))
                .leftJoin(t3).on(t3.customerId.eq(t1.id));

        query.where(criteria);
    }

    @Override
    public SupplierCustomerData transform(Object v) {

        Tuple tuple = (Tuple) v;
        CustomerEntity customer = tuple.get(t1);
        EnterpriseInfoEntity enterpriseInfo = tuple.get(t2);
        SupplierCustomerEntity supplierCustomer = tuple.get(t3);
        if (supplierCustomer == null) {
            throw new IllegalStateException();
        }

        SupplierCustomerData data = new SupplierCustomerData(supplierCustomer);
        if (customer != null) {
            CustomerData customerData = new CustomerData(customer);
            if (enterpriseInfo != null) {
                customerData.setInfo(new EnterpriseInfoData(enterpriseInfo));
            }
            data.setCustomer(customerData);
        }

        return data;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {

        private List<Long> id;

        private Long supplierId;

        private String name;

        private LocalDateTime createTimeBegin;

        private LocalDateTime createTimeEnd;

        private LocalDateTime bizCreateTimeBegin;

        private LocalDateTime bizCreateTimeEnd;

        private String contacts;

        private String contactPhone;

        private String customerNo;
    }

}
