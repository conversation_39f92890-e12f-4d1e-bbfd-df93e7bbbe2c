package com.olading.operate.labor.app.web.biz.tax;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.query.TaxPaymentVoucherQuery;
import com.olading.operate.labor.domain.service.TaxPaymentVoucherService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.tax.TaxPaymentVoucherEntity;
import com.olading.operate.labor.domain.share.tax.vo.TaxPaymentVoucherVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@Tag(name = "税务缴纳凭证相关接口")
@RestController
@RequestMapping("/api/supplier/taxpaymentvoucher")
@RequiredArgsConstructor
@Slf4j
public class TaxPaymentVoucherController extends BusinessController {

    private final TaxPaymentVoucherService taxPaymentVoucherService;
    private final QueryService queryService;

    @Operation(summary = "税务缴纳凭证记录列表")
    @PostMapping("/list")
    public WebApiQueryResponse<TaxPaymentVoucherVo> listTaxPaymentVoucher(@RequestBody QueryFilter<WebQueryTaxPaymentVoucherFilters> request) {
        QueryFilter<TaxPaymentVoucherQuery.Filters> filter = request.convert(WebQueryTaxPaymentVoucherFilters::convert);
        filter.sort("id", Direction.DESCENDING);

        DataSet<TaxPaymentVoucherVo> ds = queryService.queryTaxPaymentVoucher(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "新增税务缴纳凭证记录")
    @PostMapping("/add")
    public WebApiResponse<Long> addTaxPaymentVoucher(@Valid @RequestBody TaxPaymentVoucherParam param) {
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        BeanUtils.copyProperties(param, vo);
        vo.setSupplierId(currentSupplierId());

        TaxPaymentVoucherEntity entity = taxPaymentVoucherService.addTaxPaymentVoucher(currentTenant(), vo);
        return WebApiResponse.success(entity.getId());
    }

    @Operation(summary = "更新税务缴纳凭证记录")
    @PostMapping("/update")
    public WebApiResponse<Void> updateTaxPaymentVoucher(@Valid @RequestBody TaxPaymentVoucherParam param) {
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        BeanUtils.copyProperties(param, vo);

        taxPaymentVoucherService.updateTaxPaymentVoucher(currentTenant(), vo);
        return WebApiResponse.success();
    }

    @Operation(summary = "税务缴纳凭证记录详情")
    @PostMapping("/query")
    public WebApiResponse<TaxPaymentVoucherVo> queryTaxPaymentVoucher(@Valid @RequestBody IdRequest request) {
        TaxPaymentVoucherVo vo = taxPaymentVoucherService.queryTaxPaymentVoucher(request.getId());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "删除税务缴纳凭证记录")
    @PostMapping("/delete")
    public WebApiResponse<Void> deleteTaxPaymentVoucher(@Valid @RequestBody IdRequest request) {
        taxPaymentVoucherService.deleteTaxPaymentVoucher(currentTenant(), request.getId());
        return WebApiResponse.success();
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "税务缴纳凭证记录ID不能为空")
        @Schema(description = "税务缴纳凭证记录ID")
        private Long id;
    }

    @Data
    public static class WebQueryTaxPaymentVoucherFilters {
        @Schema(description = "税务缴纳凭证记录ID")
        private Long id;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        private String taxPaymentPeriod;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        @Schema(description = "创建时间-起")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDateTime createTimeEnd;

        public TaxPaymentVoucherQuery.Filters convert() {
            return Beans.copyBean(this, TaxPaymentVoucherQuery.Filters.class);
        }
    }

    @Data
    public static class TaxPaymentVoucherParam {
        @Schema(description = "税务缴纳凭证记录ID")
        private Long id;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        private String taxPaymentPeriod;

        @Schema(description = "附件ID，逗号分隔")
        private String fileIds;
    }
}
