package com.olading.operate.labor.domain.proxy.order;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@Comment("工资代发批次")
@Entity
@Table(name = "t_proxy_batch")
public class ProxyBatchEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("客户id")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("合同id")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @NotNull
    @Comment("工资批次id")
    @Column(name = "salary_statement_id", nullable = false)
    private Long salaryStatementId;

    @Comment("总金额")
    @Column(name = "total_amount", precision = 19, scale = 2)
    private BigDecimal totalAmount;

    @Size(max = 64)
    @NotNull
    @Comment("通道编码")
    @Column(name = "pay_channel", nullable = false, length = 64)
    private String payChannel;

    @Comment("总笔数")
    @Column(name = "count")
    private Long count;

    @Comment("确认出款时间")
    @Column(name = "confirm_time")
    private LocalDateTime confirmTime;

    @Comment("完成时间")
    @Column(name = "complete_time")
    private LocalDateTime completeTime;

    @Size(max = 256)
    @Comment("错误信息")
    @Column(name = "last_error_info", length = 256)
    private String lastErrorInfo;


    @NotNull
    @Comment("批次状态 CHECK/PROCESSING/COMPLETE")
    @Column(name = "batch_status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private ProxyBatchStatusEnum batchStatus;

    @Size(max = 256)
    @Comment("备注")
    @Column(name = "remark", length = 256)
    private String remark;

}