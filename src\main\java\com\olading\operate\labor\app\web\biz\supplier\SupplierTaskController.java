package com.olading.operate.labor.app.web.biz.supplier;


import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.Json;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.WebApiCodes;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.OrderDetailTaskQuery;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.task.TaskEntity;
import com.olading.operate.labor.domain.share.task.TaskManager;
import com.olading.operate.labor.domain.share.task.TaskStatus;
import com.olading.operate.labor.domain.share.task.TaskType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Tag(name = "异步导出交易明细任务接口")
@RestController
@RequestMapping("/api/supplier/task")
@RequiredArgsConstructor
@Slf4j
public class SupplierTaskController extends BusinessController {

    private final QueryService queryService;
    private final TaskManager taskManager;

    @Operation(summary = "查询任务")
    @PostMapping("/queryOrderDetailTask")
    public WebApiQueryResponse<TaskInfoVo> queryOrderDetailTask(@Valid @RequestBody QueryFilter<WebQueryOrderDetailTaskFilters> request) {
        WebQueryOrderDetailTaskFilters webQueryOrderDetailTaskFilters = new WebQueryOrderDetailTaskFilters(currentSupplierId(), request.getFilters().getTaskType());
        request.setFilters(webQueryOrderDetailTaskFilters);
        QueryFilter<OrderDetailTaskQuery.Filters> filter = request.convert(WebQueryOrderDetailTaskFilters::convert);
        DataSet<TaskEntity> ds = queryService.queryOrderDetailTask(filter);
        return WebApiQueryResponse.success(ds.getData().stream()
                .map(o -> {
                    TaskInfoVo vo = new TaskInfoVo();
                    vo.setTaskId(o.getId());
                    vo.setCreateTime(o.getCreateTime());
                    vo.setFileName(o.getFileName());
                    vo.setAttachments(o.getAttachments());
                    long daysBetween = ChronoUnit.DAYS.between(o.getCreateTime(), LocalDateTime.now());
                    if (daysBetween > 7) {
                        vo.setStatus("已过期");
                    } else if (o.getTaskStatus() == TaskStatus.FAILED) {
                        vo.setStatus("失败");
                    } else if (o.getTaskStatus() == TaskStatus.RUNNING || o.getTaskStatus() == TaskStatus.WAITING) {
                        vo.setStatus("生成中");
                    } else if (o.getTaskStatus() == TaskStatus.SUCCESS) {
                        vo.setStatus("已生成");
                    }
                    return vo;
                }).collect(Collectors.toList()), ds.getTotal());
    }


    @Operation(summary = "保存任务")
    @PostMapping("/saveOrderDetailTask")
    public WebApiResponse<String> saveOrderDetailTask(@Valid @RequestBody OrderDetailRequest request) {
        TenantInfo tenant = currentTenant();
        OwnerType ownerType;
        switch (tenant.getType()) {
            case SUPPLIER -> ownerType = OwnerType.SUPPLIER;
            case BOSS -> ownerType = OwnerType.BOSS;
            default -> throw new IllegalStateException();
        }
        long ownerId = currentSupplierId();
        String fileName = null;
        if (request.getTaskType() == TaskType.WECHAT) {
            fileName = generateFileName("微信转账到零钱交易明细下载-");
        } else if (request.getTaskType() == TaskType.ALIPAY) {
            fileName = generateFileName("支付宝交易明细下载-");
        } else if (request.getTaskType() == TaskType.WECHAT_RP) {
            fileName = generateFileName("微信红包交易明细下载-");
        } else if (request.getTaskType() == TaskType.WECHAT_COUPON) {
            fileName = generateFileName("微信代金券交易明细下载-");
        } else if (request.getTaskType() == TaskType.ALIPAY_COUPON) {
            fileName = generateFileName("数字分行红包交易明细下载-");
        } else if (request.getTaskType() == TaskType.WECHAT_TB) {
            fileName = generateFileName("微信平台商家转账交易明细下载-");
        }
        request.setSupplierId(ownerId);
        TaskEntity taskEntity = taskManager.submit(ownerId, ownerType, request.getTaskType(), Json.toJson(request), fileName);
        if (taskEntity == null) {
            return WebApiResponse.fail(WebApiCodes.BUSINESS_FAIL, "任务提交失败");
        }
        return WebApiResponse.success("任务提交成功");
    }


    @Operation(summary = "删除任务")
    @PostMapping("deleteOrderDetailTask")
    public WebApiResponse<Void> deleteOrderDetailTask(@RequestBody DeleteTaskRequest request) {
        taskManager.delete(request.getTaskId());
        return WebApiResponse.success();
    }


    private String generateFileName(String fileName) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String date = currentDate.format(formatter);
        // 生成四位随机码
        Random random = new Random();
        int randomCode = 1000 + random.nextInt(9000); // 生成1000到9999之间的随机数
        // 拼接文件名称
        return fileName + date + "-" + randomCode + ".zip";
    }


    @Data
    public static class TaskInfoVo {
        private Long taskId;
        private LocalDateTime createTime;
        private String fileName;
        private String status;
        private List<String> attachments;
    }

    @Data
    public static class OrderDetailRequest {
        private long supplierId;
        private List<Long> customerIds;
        private String customerId;
        @Schema(description = "订单号")
        private String orderNo;
        @NotNull
        private LocalDateTime createTimeBegin;
        @NotNull
        private LocalDateTime createTimeEnd;
        @Schema(description = "领取人openid")
        private String payeeAccountNo;
        @Schema(description = "姓名")
        private String payeeName;
        @Schema(description = "任务类型")
        private TaskType taskType;
        @Schema(description = "批次号")
        private String batchNo;
        @Schema(description = "优惠劵id")
        private String couponId;
    }

    @Data
    @AllArgsConstructor
    public static class WebQueryOrderDetailTaskFilters {
        private Long ownerId;

        private TaskType taskType;

        public OrderDetailTaskQuery.Filters convert() {
            return Beans.copyBean(this, OrderDetailTaskQuery.Filters.class);
        }
    }

    @Data
    public static class DeleteTaskRequest {
        private Long taskId;
    }
}
