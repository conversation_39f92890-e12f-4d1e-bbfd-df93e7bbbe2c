package com.olading.operate.labor.domain.corporation;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CorporationConfigData{

    @Schema(description = "id")
    private Long id;

    @Schema(description = "灵工平台id")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "最小年龄限制")
    private Integer minAgeLimit;

    @Schema(description = "最大年龄限制")
    private Integer maxAgeLimit;

    @Schema(description = "发票类目配置")
    private String invoiceCategory;

    @Schema(description = "增值税起征点(元)")
    private BigDecimal vatStart;

    @Schema(description = "增值税税率(%)")
    private BigDecimal vatRate;

    @Schema(description = "附加税配置(json)")
    private String surtaxData;

    public List<String> getInvoiceCategoryList() {
        return StringUtils.isNotBlank(invoiceCategory)? List.of(invoiceCategory.split(",")) : List.of();
    }

    public List<SurtaxData> getSurtax() {
        return StringUtils.isNotBlank(surtaxData)? JSONUtil.toList(surtaxData, SurtaxData.class) : List.of();
    }
}