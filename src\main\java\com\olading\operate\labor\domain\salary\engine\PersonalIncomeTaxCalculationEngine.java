package com.olading.operate.labor.domain.salary.engine;

import cn.hutool.json.JSONUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.TaxDeclareStatusEnum;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.SurtaxData;
import com.olading.operate.labor.domain.salary.PreviousIncomeDeductionEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.salary.engine.dto.AccumulatedTaxData;
import com.olading.operate.labor.domain.salary.engine.dto.SurtaxCalculationResult;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationRequest;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationResult;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareManager;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDetailEntity;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDetailManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人所得税计算引擎
 *
 * 算税规则：
 * 1. 1月份：累计数据全部为0
 * 2. 非1月份第一笔：查询上月个税申报记录 -> 查询上期收入减除导入记录 -> 都没有则为0
 * 3. 非第一笔：获取同期上一笔已确认的薪酬明细累计数据
 * 4. 累计逻辑：减除费用按月累加，其他项按实际传入值累加
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PersonalIncomeTaxCalculationEngine {

    /**
     * 每月减除费用
     */
    public static final BigDecimal MONTHLY_SUBTRAHEND = BigDecimal.valueOf(5000);
    private final SalaryManager salaryManager;
    private final PersonalIncomeTaxDetailManager taxDetailManager;
    private final PersonalIncomeTaxDeclareManager taxDeclareManager;
    private final PreviousIncomeDeductionManager previousIncomeManager;
    private final CorporationManager corporationManager;

    /**
     * 个税计算主入口
     */
    public TaxCalculationResult calculatePersonalIncomeTax(TaxCalculationRequest request) {
        log.info("开始计算个税，身份证：{}，作业主体：{}，税期：{}",
                request.getIdCard(), request.getSupplierCorporationId(), request.getTaxPeriod());

        try {
            // 1. 获取累计数据
            AccumulatedTaxData accumulatedData = getAccumulatedTaxData(request);

            // 2. 执行个税计算
            TaxCalculationResult result = performTaxCalculation(request, accumulatedData);

            log.info("个税计算完成，本期应预扣预缴税额：{}，实发金额：{}，其他税费：{}",
                    result.getCurrentWithholdingTax(), result.getNetPayment(),JSONUtil.toJsonStr(result));

            return result;
        } catch (Exception e) {
            log.error("个税计算失败，身份证：{}，作业主体：{}，税期：{}",
                    request.getIdCard(), request.getSupplierCorporationId(), request.getTaxPeriod(), e);
            throw new RuntimeException("个税计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取累计税务数据
     */
    private AccumulatedTaxData getAccumulatedTaxData(TaxCalculationRequest request) {
        String taxPeriod = request.getTaxPeriod();
        String idCard = request.getIdCard();
        Long supplierCorporationId = request.getSupplierCorporationId();



        // 判断是否为个人在该主体下的第一笔
        boolean isFirstRecord = isFirstRecordForPerson(idCard, supplierCorporationId, taxPeriod);

        if (isFirstRecord) {

            // 1月份直接返回零值累计数据
            if (isJanuary(taxPeriod)) {
                log.info("1月份算税，累计数据为0");
                return AccumulatedTaxData.zero();
            }

            log.info("个人在该主体下的第一笔收入，查询历史累计数据");
            // 尝试获取历史累计数据，如果获取不到则返回零值
            AccumulatedTaxData historicalData = getHistoricalAccumulatedData(
                    idCard, supplierCorporationId, taxPeriod);

            return historicalData != null ? historicalData : AccumulatedTaxData.zero();
        } else {
            log.info("个人在该主体下的非第一笔收入，获取同期上一笔薪酬明细累计数据");
            // 获取同期上一笔已确认的薪酬明细累计数据
            return getFromPreviousSalaryDetail(idCard, supplierCorporationId, taxPeriod);
        }
    }

    /**
     * 获取历史累计数据
     */
    private AccumulatedTaxData getHistoricalAccumulatedData(String idCard,
                                                            Long supplierCorporationId, String taxPeriod) {

        // 1. 优先查询上月个税申报记录
        AccumulatedTaxData fromLastMonthDeclare = getFromLastMonthTaxDeclare(
                idCard, supplierCorporationId, taxPeriod);

        if (fromLastMonthDeclare != null) {
            log.info("从上月个税申报记录获取累计数据");
            return fromLastMonthDeclare;
        }

        // 2. 查询上期收入减除导入记录
        AccumulatedTaxData fromPreviousImport = getFromPreviousIncomeDeduction(
                idCard, supplierCorporationId, taxPeriod);

        if (fromPreviousImport != null) {
            log.info("从上期收入减除导入记录获取累计数据");
            return fromPreviousImport;
        }

        // 3. 如果都查不到，返回null（表示按0计算）
        log.info("未找到历史累计数据，按0计算");
        return null;
    }

    /**
     * 查询上月个税申报记录累计数据
     */
    private AccumulatedTaxData getFromLastMonthTaxDeclare(String idCard,
                                                          Long supplierCorporationId, String taxPeriod) {

        String lastMonth = calculateLastMonth(taxPeriod);

        // 查询上月已申报的个税申报记录
        PersonalIncomeTaxDeclareEntity lastDeclare = taxDeclareManager
                .queryPersonalIncomeTaxDeclareBySupplierCorporationAndTaxPeriod(
                        supplierCorporationId, lastMonth);

        if (lastDeclare == null || !TaxDeclareStatusEnum.DECLARED.name().equals(lastDeclare.getTaxStatus())) {
            return null;
        }

        // 查询该申报记录下的个人明细
        PersonalIncomeTaxDetailEntity detail = taxDetailManager
                .queryDetailByTaxDeclareIdAndIdCard(lastDeclare.getId(), idCard);

        if (detail == null) {
            return null;
        }

        // 转换为累计数据
        return convertToAccumulatedData(detail);
    }

    /**
     * 查询上期收入减除导入记录
     */
    private AccumulatedTaxData getFromPreviousIncomeDeduction(String idCard,
                                                              Long supplierCorporationId, String taxPeriod) {

        String lastMonth = calculateLastMonth(taxPeriod);

        PreviousIncomeDeductionEntity entity = previousIncomeManager
                .queryByIdCardAndCorporationAndPeriod(idCard, supplierCorporationId, lastMonth);

        if (entity == null) {
            return null;
        }

        return convertToAccumulatedData(entity);
    }

    /**
     * 查询同期上一笔薪酬明细累计数据
     */
    private AccumulatedTaxData getFromPreviousSalaryDetail(String idCard,
                                                           Long supplierCorporationId, String taxPeriod) {

        SalaryDetailEntity lastDetail = salaryManager
                .queryLastConfirmedSalaryDetail(idCard, supplierCorporationId, taxPeriod);

        if (lastDetail == null) {
            return AccumulatedTaxData.zero();
        }

        return convertToAccumulatedData(lastDetail);
    }

    /**
     * 执行个税计算 - 根据新公式调整
     */
    private TaxCalculationResult performTaxCalculation(TaxCalculationRequest request,
                                                       AccumulatedTaxData accumulatedData) {

        // 1. 计算新的累计数据
        AccumulatedTaxData newAccumulated = new AccumulatedTaxData();

        // 累计收入 = 上期累计收入 + 本期应发金额
        newAccumulated.setAccumulatedIncome(
                accumulatedData.getAccumulatedIncome().add(request.getPayableAmount()));

        // 累计费用 = 累计收入 * 20%
        newAccumulated.setAccumulatedExpenses(
                newAccumulated.getAccumulatedIncome().multiply(new BigDecimal("0.20")));

        // 累计减除费用 = 按月数计算,中断重新计算 (获取的上月累计值 + 5000)
        newAccumulated.setAccumulatedDeductionExpenses(accumulatedData.getAccumulatedDeductionExpenses().add(MONTHLY_SUBTRAHEND));

        // 累计免税收入 = 上期累计免税收入 + 本期免税收入 (累加逻辑)
        newAccumulated.setAccumulatedTaxFreeIncome(
                accumulatedData.getAccumulatedTaxFreeIncome().add(request.getTaxFreeIncome()));

        // 累计其他扣除 = 上期累计其他扣除 + 本期其他扣除 (累加逻辑)
        newAccumulated.setAccumulatedOtherDeductions(
                accumulatedData.getAccumulatedOtherDeductions().add(request.getOtherDeductions()));

        // 累计减免税额 = 上期累计减免税额 + 本期减免税额 (累加逻辑)
        newAccumulated.setAccumulatedTaxRelief(
                accumulatedData.getAccumulatedTaxRelief().add(request.getTaxReliefAmount()));

        // 2. 计算累计应纳税所得额
        // 累计应纳税所得额 = 累计收入 - 累计费用 - 累计减除费用 - 累计免税收入 - 累计依法确定的其他扣除
        BigDecimal accumulatedTaxableAmount = newAccumulated.getAccumulatedIncome()
                .subtract(newAccumulated.getAccumulatedExpenses())
                .subtract(newAccumulated.getAccumulatedDeductionExpenses())
                .subtract(newAccumulated.getAccumulatedTaxFreeIncome())
                .subtract(newAccumulated.getAccumulatedOtherDeductions());

        // 应纳税所得额不能为负数
        accumulatedTaxableAmount = accumulatedTaxableAmount.max(BigDecimal.ZERO);
        newAccumulated.setAccumulatedTaxableAmount(accumulatedTaxableAmount);

        // 3. 计算累计应纳税额
        // 累计应纳税额 = (累计收入-累计费用-累计免税收入-累计减除费用-累计依法确定的其他扣除) × 预扣率 - 速算扣除数
        BigDecimal accumulatedTaxAmount = calculateTaxByTaxTable(accumulatedTaxableAmount);
        // 累计应纳税额不能为负数
        accumulatedTaxAmount = accumulatedTaxAmount.max(BigDecimal.ZERO);
        newAccumulated.setAccumulatedTaxAmount(accumulatedTaxAmount);

        // 4. 计算本期应预扣预缴税额（理论增量） = 累计应纳税额 - 累计已预缴税额（上月）- 累计减免税额
        BigDecimal currentTaxAmount = accumulatedTaxAmount
                .subtract(accumulatedData.getAccumulatedPrepaidTax())
                .subtract(newAccumulated.getAccumulatedTaxRelief())
                // 如果为负数则直接等于0
                .max(BigDecimal.ZERO);
        newAccumulated.setAccumulatedCurrentTaxAmount(currentTaxAmount);

        // 5. 计算本次应纳税所得额
        // 计算本次应纳税所得额 = 累计应纳税所得额-累计应纳税所得额（上期）
        //BigDecimal currentTaxableAmount = accumulatedTaxableAmount;

        // 6. 计算本次应预扣预缴税额（实际扣款）
        // 本次应预扣预缴税额 = 本期应预扣预缴税额（理论增量）- 当期累计已扣缴税额
        BigDecimal currentWithholdingTax = currentTaxAmount
                .subtract(accumulatedData.getAccumulatedCurrentTaxAmount())
                // 不能为负数
                .max(BigDecimal.ZERO);

        // 7. 更新累计已预缴税额 累计已预缴税额(截至上个月)
        newAccumulated.setAccumulatedPrepaidTax(
                accumulatedData.getAccumulatedPrepaidTax());



        // 10. 计算增值税和附加税（增量计算方式）
        BigDecimal monthlyConfirmedAmount = salaryManager.queryMonthlyTotalPayableAmount(
                request.getSupplierCorporationId(), request.getTaxPeriod());
        final CorporationConfigEntity corporationConfig = corporationManager.getCorporationConfig(request.getSupplierCorporationId());
        BigDecimal vatAmount = calculateVATIncremental(corporationConfig.getVatStart().multiply(BigDecimal.valueOf(10000)),corporationConfig.getVatRate(),monthlyConfirmedAmount, request.getPayableAmount());
        SurtaxCalculationResult surtaxResult = calculateSurtax(corporationConfig,vatAmount, request.getSupplierCorporationId());

        // 11. 重新计算实发金额（扣除增值税和附加税）
        BigDecimal totalTaxDeduction = currentWithholdingTax.add(vatAmount).add(surtaxResult.getTotalSurtax());
        BigDecimal netPayment = request.getPayableAmount().subtract(totalTaxDeduction);

        // 12. 构建返回结果
        TaxCalculationResult result = new TaxCalculationResult();
        result.setNewAccumulatedData(newAccumulated);
        result.setCurrentTaxAmount(currentTaxAmount);
        // 本次应预扣预缴税额（实际扣款）
        result.setCurrentWithholdingTax(currentWithholdingTax);
        result.setNetPayment(netPayment);

        // 设置增值税和附加税
        result.setVatAmount(vatAmount);
        result.setAdditionalTaxAmount(surtaxResult.getTotalSurtax());
        result.setUrbanConstructionTax(surtaxResult.getUrbanConstructionTax());
        result.setEducationSurcharge(surtaxResult.getEducationSurcharge());
        result.setLocalEducationSurcharge(surtaxResult.getLocalEducationSurcharge());

        return result;
    }

    /**
     * 根据税率表计算税额
     */
    private BigDecimal calculateTaxByTaxTable(BigDecimal taxableAmount) {
        // 个税税率表 (2024年标准)
        if (taxableAmount.compareTo(new BigDecimal("36000")) <= 0) {
            // 3%
            return taxableAmount.multiply(new BigDecimal("0.03"));
        } else if (taxableAmount.compareTo(new BigDecimal("144000")) <= 0) {
            // 10%
            return taxableAmount.multiply(new BigDecimal("0.10")).subtract(new BigDecimal("2520"));
        } else if (taxableAmount.compareTo(new BigDecimal("300000")) <= 0) {
            // 20%
            return taxableAmount.multiply(new BigDecimal("0.20")).subtract(new BigDecimal("16920"));
        } else if (taxableAmount.compareTo(new BigDecimal("420000")) <= 0) {
            // 25%
            return taxableAmount.multiply(new BigDecimal("0.25")).subtract(new BigDecimal("31920"));
        } else if (taxableAmount.compareTo(new BigDecimal("660000")) <= 0) {
            // 30%
            return taxableAmount.multiply(new BigDecimal("0.30")).subtract(new BigDecimal("52920"));
        } else if (taxableAmount.compareTo(new BigDecimal("960000")) <= 0) {
            // 35%
            return taxableAmount.multiply(new BigDecimal("0.35")).subtract(new BigDecimal("85920"));
        } else {
            // 45%
            return taxableAmount.multiply(new BigDecimal("0.45")).subtract(new BigDecimal("181920"));
        }
    }

    /**
     * 判断是否为1月份
     */
    private boolean isJanuary(String taxPeriod) {
        return taxPeriod.endsWith("-01");
    }

    /**
     * 判断是否为个人在该主体下的第一笔记录
     */
    private boolean isFirstRecordForPerson(String idCard, Long supplierCorporationId, String taxPeriod) {
        return salaryManager.countSalaryDetailByIdCardAndCorporationAndPeriod(
                idCard, supplierCorporationId, taxPeriod) == 1;
    }

    /**
     * 计算上个月
     */
    private String calculateLastMonth(String taxPeriod) {
        // taxPeriod格式: "2024-01"
        String[] parts = taxPeriod.split("-");
        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);

        if (month == 1) {
            year--;
            month = 12;
        } else {
            month--;
        }

        return String.format("%d-%02d", year, month);
    }

    /**
     * 获取当前月份数字 (1-12)
     */
    private int getCurrentMonthNumber(String taxPeriod) {
        // taxPeriod格式: "2024-01"
        String[] parts = taxPeriod.split("-");
        return Integer.parseInt(parts[1]);
    }

    /**
     * 转换个税申报明细为累计数据
     */
    private AccumulatedTaxData convertToAccumulatedData(PersonalIncomeTaxDetailEntity detail) {
        AccumulatedTaxData data = new AccumulatedTaxData();
        data.setAccumulatedIncome(detail.getAccumulatedIncome());
        data.setAccumulatedExpenses(detail.getAccumulatedExpenses());
        data.setAccumulatedDeductionExpenses(detail.getAccumulatedTaxDeductionExpenses());
        data.setAccumulatedTaxFreeIncome(detail.getAccumulatedTaxFreeIncome());
        data.setAccumulatedOtherDeductions(detail.getAccumulatedOtherDeductions());
        data.setAccumulatedTaxRelief(detail.getAccumulatedTaxReductions());
        // 需要重新计算
        data.setAccumulatedTaxableAmount(BigDecimal.ZERO);
        data.setAccumulatedTaxAmount(detail.getAccumulatedPrepaidTax());
        data.setAccumulatedPrepaidTax(detail.getAccumulatedPrepaidTax());
        return data;
    }

    /**
     * 转换上期收入减除导入记录为累计数据
     */
    private AccumulatedTaxData convertToAccumulatedData(PreviousIncomeDeductionEntity entity) {
        AccumulatedTaxData data = new AccumulatedTaxData();
        data.setAccumulatedIncome(entity.getAccumulatedIncome());
        data.setAccumulatedExpenses(entity.getAccumulatedExpenses());
        data.setAccumulatedDeductionExpenses(MONTHLY_SUBTRAHEND);
        data.setAccumulatedTaxFreeIncome(entity.getAccumulatedTaxFreeIncome());
        data.setAccumulatedOtherDeductions(entity.getAccumulatedOtherDeductions());
        data.setAccumulatedTaxRelief(entity.getAccumulatedTaxReductions());
        // 需要重新计算
        data.setAccumulatedTaxableAmount(BigDecimal.ZERO);
        // 需要重新计算
        data.setAccumulatedTaxAmount(entity.getAccumulatedPrepaidTax());
        data.setAccumulatedPrepaidTax(entity.getAccumulatedPrepaidTax());
        return data;
    }

    /**
     * 转换薪酬明细为累计数据
     */
    private AccumulatedTaxData convertToAccumulatedData(SalaryDetailEntity detail) {
        AccumulatedTaxData data = new AccumulatedTaxData();
        data.setAccumulatedIncome(detail.getAccumulatedIncome());
        data.setAccumulatedExpenses(detail.getAccumulatedExpenses());
        data.setAccumulatedDeductionExpenses(detail.getAccumulatedDeductionExpenses().subtract(MONTHLY_SUBTRAHEND));
        data.setAccumulatedTaxFreeIncome(detail.getAccumulatedTaxFreeIncome());
        data.setAccumulatedOtherDeductions(detail.getAccumulatedOtherDeductions());
        data.setAccumulatedTaxRelief(detail.getAccumulatedTaxRelief());
        data.setAccumulatedTaxableAmount(BigDecimal.ZERO);
        data.setAccumulatedTaxAmount(detail.getAccumulatedTaxAmount());
        data.setAccumulatedPrepaidTax(detail.getAccumulatedPrepaidTax());
        data.setAccumulatedCurrentTaxAmount(detail.getCurrentTaxAmount());
        return data;
    }

    /**
     * 计算附加税
     * 基于增值税额计算，包含：城市维护建设税、教育费附加、地方教育附加
     *
     * @param vatAmount 增值税额
     * @param supplierCorporationId 作业主体ID
     * @return 附加税计算结果
     */
    private SurtaxCalculationResult calculateSurtax(CorporationConfigEntity config, BigDecimal vatAmount, Long supplierCorporationId) {
        if (vatAmount.compareTo(BigDecimal.ZERO) == 0) {
            log.info("增值税额为0，附加税为0");
            return SurtaxCalculationResult.zero();
        }

        try {
            // 获取作业主体配置
            if (config == null || config.getSurtaxData() == null) {
                log.warn("作业主体{}未配置附加税信息，附加税为0", supplierCorporationId);
                return SurtaxCalculationResult.zero();
            }

            // 解析附加税配置
            List<SurtaxData> surtaxDataList = parseSurtaxData(config.getSurtaxData());
            if (surtaxDataList == null || surtaxDataList.isEmpty()) {
                log.warn("作业主体{}附加税配置为空，附加税为0", supplierCorporationId);
                return SurtaxCalculationResult.zero();
            }

            // 计算各项附加税
            SurtaxCalculationResult result = new SurtaxCalculationResult();
            for (SurtaxData surtaxData : surtaxDataList) {
                if (surtaxData.getRate() == null || surtaxData.getDiscount_rate() == null) {
                    continue;
                }

                // 附加税 = 增值税额 × 附加税税率 × (1 - 优惠比例)
                BigDecimal surtaxAmount = vatAmount
                        .multiply(surtaxData.getRate().divide(BigDecimal.valueOf(100)))
                        .multiply(BigDecimal.ONE.subtract(surtaxData.getDiscount_rate()))
                        .setScale(2, BigDecimal.ROUND_HALF_UP);

                switch (surtaxData.getSurtaxCode()) {
                    case URBAN_MAINTENANCE_TAX:
                        result.setUrbanConstructionTax(surtaxAmount);
                        log.info("城市维护建设税：{}", surtaxAmount);
                        break;
                    case EDUCATION_SURCHARGE:
                        result.setEducationSurcharge(surtaxAmount);
                        log.info("教育费附加：{}", surtaxAmount);
                        break;
                    case LOCAL_EDUCATION_SURCHARGE:
                        result.setLocalEducationSurcharge(surtaxAmount);
                        log.info("地方教育附加：{}", surtaxAmount);
                        break;
                    default:
                        log.warn("未知的附加税类型：{}", surtaxData.getSurtaxCode());
                        break;
                }
            }

            log.info("附加税计算完成，总额：{}", result.getTotalSurtax());
            return result;

        } catch (Exception e) {
            log.error("计算附加税失败，作业主体：{}，增值税额：{}", supplierCorporationId, vatAmount, e);
            return SurtaxCalculationResult.zero();
        }
    }

    /**
     * 解析附加税配置数据
     *
     * @param surtaxDataJson 附加税配置JSON字符串
     * @return 附加税配置列表
     */
    private List<SurtaxData> parseSurtaxData(String surtaxDataJson) {
        try {
            if (surtaxDataJson == null || surtaxDataJson.trim().isEmpty()) {
                return null;
            }

            // 使用hutool的JSONUtil解析
            return JSONUtil.toList(surtaxDataJson, SurtaxData.class);

        } catch (Exception e) {
            log.error("解析附加税配置失败：{}", surtaxDataJson, e);
            return null;
        }
    }

    /**
     * 增量计算增值税（按你建议的逻辑）
     *
     * @param monthlyConfirmedAmount 当月已确认的应发金额累计
     * @param currentPayableAmount 当前应发金额
     * @return 当前这笔应承担的增值税额
     */
    private BigDecimal calculateVATIncremental(BigDecimal vatStartAmount, BigDecimal vatRateO, BigDecimal monthlyConfirmedAmount, BigDecimal currentPayableAmount) {
        BigDecimal vatThreshold = vatStartAmount; // 起征点10万
        BigDecimal vatRate = vatRateO.divide(BigDecimal.valueOf(100)); // 1%税率

        log.info("增量计算增值税，当月已确认累计：{}，当前应发：{}", monthlyConfirmedAmount, currentPayableAmount);

        // 1. 判断当月已确认累计是否已超过起征点
        if (monthlyConfirmedAmount.compareTo(vatThreshold) >= 0) {
            // 已超过起征点，直接用本期应发金额计算增值税
            BigDecimal vatAmount = currentPayableAmount.multiply(vatRate).setScale(2, BigDecimal.ROUND_HALF_UP);;
            log.info("当月已确认累计{}已超过起征点，本期增值税 = {} × {} = {}",
                    monthlyConfirmedAmount, currentPayableAmount, vatRate, vatAmount);
            return vatAmount;
        }

        // 2. 当月已确认累计未超过起征点，判断加上本期后是否超过
        BigDecimal totalAmount = monthlyConfirmedAmount.add(currentPayableAmount);
        if (totalAmount.compareTo(vatThreshold) <= 0) {
            // 加上本期后仍未超过起征点，增值税为0
            log.info("当月累计{}加上本期{}仍未超过起征点{}，增值税为0",
                    monthlyConfirmedAmount, currentPayableAmount, vatThreshold);
            return BigDecimal.ZERO;
        }

        // 3. 加上本期后超过起征点，计算超过部分的增值税
        BigDecimal excessAmount = totalAmount;
        BigDecimal vatAmount = excessAmount.multiply(vatRate);
        log.info("当月累计{}加上本期{}超过起征点{}，超过部分{}，增值税 = {} × {} = {}",
                monthlyConfirmedAmount, currentPayableAmount, vatThreshold, excessAmount, excessAmount, vatRate, vatAmount);
        return vatAmount;
    }
}