package com.olading.operate.labor.domain.service;

import com.olading.operate.labor.domain.share.identity.IdentifyProvider;
import com.olading.operate.labor.domain.share.identity.dto.FaceAuthResult;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 身份识别服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IdentityService {

    private final IdentifyProvider identifyProvider;

    /**
     * OCR身份证识别
     * 
     * @param personalIdImg 个人证件照片面图片base64（必填）
     * @param nationalEmblemImg 国徽面图片base64（可选）
     * @return OCR识别结果
     */
    public OcrIdentifyResult ocrIdentify(String personalIdImg, String nationalEmblemImg) {
        log.info("开始OCR身份证识别");
        
        if (personalIdImg == null || personalIdImg.trim().isEmpty()) {
            return OcrIdentifyResult.failure("个人证件照片面图片不能为空", null);
        }
        
        try {
            OcrIdentifyResult result = identifyProvider.ocrIdentifyWithResult(nationalEmblemImg, personalIdImg);
            
            if (result.isSuccess()) {
                log.info("OCR身份证识别成功: flowNo={}, name={}, idNo={}", 
                        result.getFlowNo(), result.getName(), result.getIdNo());
            } else {
                log.warn("OCR身份证识别失败: flowNo={}, error={}", 
                        result.getFlowNo(), result.getErrorMessage());
            }
            
            return result;
        } catch (Exception e) {
            log.error("OCR身份证识别异常", e);
            return OcrIdentifyResult.failure("OCR识别服务异常", null);
        }
    }

    /**
     * OCR身份证识别（只传个人证件照片面）
     * 
     * @param personalIdImg 个人证件照片面图片base64
     * @return OCR识别结果
     */
    public OcrIdentifyResult ocrIdentify(String personalIdImg) {
        return ocrIdentify(personalIdImg, null);
    }

    /**
     * 活体人脸识别
     * 
     * @param name 姓名
     * @param idNo 身份证号
     * @param videoBase64 视频base64
     * @return 人脸识别结果
     */
    public FaceAuthResult liveFaceAuth(String name, String idNo, String videoBase64) {
        log.info("开始活体人脸识别: name={}, idNo={}", name, idNo);
        
        if (name == null || name.trim().isEmpty()) {
            return FaceAuthResult.failure("姓名不能为空", null);
        }
        
        if (idNo == null || idNo.trim().isEmpty()) {
            return FaceAuthResult.failure("身份证号不能为空", null);
        }
        
        if (videoBase64 == null || videoBase64.trim().isEmpty()) {
            return FaceAuthResult.failure("视频数据不能为空", null);
        }
        
        try {
            FaceAuthResult result = identifyProvider.liveFaceAuthWithResult(name, idNo, videoBase64);
            
            if (result.isSuccess()) {
                log.info("活体人脸识别成功: flowNo={}, similarity={}, liveRate={}, passed={}", 
                        result.getFlowNo(), result.getSimilarity(), result.getLiveRate(), result.isPassed());
            } else {
                log.warn("活体人脸识别失败: flowNo={}, error={}", 
                        result.getFlowNo(), result.getErrorMessage());
            }
            
            return result;
        } catch (Exception e) {
            log.error("活体人脸识别异常", e);
            return FaceAuthResult.failure("活体人脸识别服务异常", null);
        }
    }

    /**
     * 身份证OCR识别并验证
     * 
     * @param personalIdImg 个人证件照片面图片base64
     * @param expectedName 期望的姓名（用于验证）
     * @param expectedIdNo 期望的身份证号（用于验证）
     * @return OCR识别结果，包含验证信息
     */
    public OcrIdentifyResult ocrIdentifyAndVerify(String personalIdImg, String expectedName, String expectedIdNo) {
        log.info("开始OCR身份证识别并验证: expectedName={}, expectedIdNo={}", expectedName, expectedIdNo);
        
        OcrIdentifyResult result = ocrIdentify(personalIdImg);
        
        if (!result.isSuccess()) {
            return result;
        }
        
        // 验证姓名和身份证号是否匹配
        boolean nameMatch = expectedName != null && expectedName.equals(result.getName());
        boolean idNoMatch = expectedIdNo != null && expectedIdNo.equals(result.getIdNo());
        
        if (!nameMatch || !idNoMatch) {
            String errorMsg = String.format("身份信息不匹配: 姓名匹配=%s, 身份证号匹配=%s", nameMatch, idNoMatch);
            log.warn("OCR识别验证失败: {}", errorMsg);
            return OcrIdentifyResult.failure(errorMsg, result.getFlowNo());
        }
        
        log.info("OCR身份证识别验证成功: flowNo={}", result.getFlowNo());
        return result;
    }
}