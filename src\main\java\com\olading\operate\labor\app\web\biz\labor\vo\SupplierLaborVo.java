package com.olading.operate.labor.app.web.biz.labor.vo;

import cn.hutool.core.bean.BeanUtil;
import com.olading.boot.util.DataSet;
import com.olading.operate.labor.app.web.biz.enums.EmpStatusEnum;
import com.olading.operate.labor.domain.query.LaborQuery;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SupplierLaborVo extends SupplierLaborListVo {

    @Schema(description = "劳务人员ID,编辑必传")
    private Long id;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "平台ID")
    private Long supplierId;
    @Schema(description = "平台名")
    private String supplierName;

    @Schema(description = "作业主体")
    private Long corporationId;

    @Schema(description = "作业主体名")
    private String corporationName;

    @Schema(description = "客户ID")
    private Long customerId;
    @Schema(description = "客户名")
    private String customerName;

    @Schema(description = "服务合同ID")
    private Long contractId;
    @Schema(description = "服务合同ID")
    private String contractName;

    @Schema(description = "最高学历")
    private String education;

    @Schema(description = "籍贯")
    private String nativeField;

    @Schema(description = "户口性质")
    private String householdRegistrationType;

    @Schema(description = "户口所在城市")
    private String householdCity;

    @Schema(description = "户口所在地址")
    private String householdAddress;

    @Schema(description = "婚姻状况")
    private String maritalStatus;

    @Schema(description = "子女状况")
    private String children;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "政治面貌")
    private String political;

    @Schema(description = "参加工作日期")
    private LocalDate inWorkDay;

    @Schema(description = "所在部门ID")
    private Long deptId;

    @Schema(description = "雇佣人员")
    private Long employed;

    @Schema(description = "加入日期")
    private LocalDate joinDate;

    @Schema(description = "岗位")
    private String post;

    @Schema(description = "员工状态 ON_THE_JOB:在职，QUIT:离职")
    private String empStatus;

    @Schema(description = "工作邮箱")
    private String workEmail;

    @Schema(description = "企业微信")
    private String wechat;

    @Schema(description = "个人微信")
    private String personalWechat;

    @Schema(description = "工作电话")
    private String workMobile;

    @Schema(description = "分机号")
    private String mobileNumber;

    @Schema(description = "个人邮箱")
    private String personalEmail;

    @Schema(description = "居住地")
    private String address;

    @Schema(description = "工资卡号")
    private String bankCard;

    @Schema(description = "开户城市")
    private String openCardCity;

    @Schema(description = "开户行")
    private String cardBank;

    @Schema(description = "开户支行")
    private String bankBranch;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;;

    @Schema(description = "更新时间")
    private LocalDateTime modifyTime;

    public static List<SupplierLaborVo> builder(DataSet<LaborQuery.Record> dataSet) {
        return dataSet.getData().stream().map(labor -> {
            SupplierLaborVo vo = new SupplierLaborVo();
            vo.setId(labor.getLaborInfo().getId());
            vo.setName(labor.getLabor().getName());
            vo.setIdCard(labor.getLabor().getIdCard());
            vo.setCellphone(labor.getLabor().getCellphone());
//            vo.setSignStatus(labor.getLabor().getSign);
            vo.setCorporationId(labor.getLaborInfo().getSupplierCorporationId());
//            vo.setCorporationName();
            vo.setCustomerId(labor.getLaborInfo().getCustomerId());
            vo.setContractId(labor.getLaborInfo().getCustomerId());
//            vo.setCustomerName(labor.getLaborInfo().getCustomerName());
            vo.setEmpStatus(labor.getLaborInfo().getEmpStatus());
            vo.setCreateTime(labor.getLaborInfo().getCreateTime());
            vo.setModifyTime(labor.getLaborInfo().getModifyTime());
            vo.setSupplierName(labor.getSupplierName());
            vo.setCorporationName(labor.getCorporationName());
            vo.setCustomerName(labor.getCustomerName());
            vo.setContractName(labor.getContractName());
            return vo;
        }).toList();
    }


    public static SupplierLaborVo builder(LaborInfoEntity laborInfoEntity, SupplierLaborEntity laborEntity) {
        SupplierLaborVo vo = new SupplierLaborVo();
        BeanUtil.copyProperties(laborInfoEntity, vo);
        vo.setId(laborEntity.getId());
        vo.setName(laborEntity.getName());
        vo.setIdCard(laborEntity.getIdCard());
        vo.setCellphone(laborEntity.getCellphone());
//            vo.setSignStatus(labor.getLabor().getSign);
        vo.setCorporationId(laborInfoEntity.getSupplierCorporationId());
        vo.setBirthdayDate(laborEntity.getBirthdayDate());
        vo.setIdCardPeriod(laborEntity.getIdCardPeriod());
//            vo.setCorporationName();
//            vo.setCustomerName(labor.getLaborInfo().getCustomerName());
        return vo;
    }

    public LaborInfoEntity toLaborInfo(LaborInfoEntity entity) {
        if (entity == null) {
            entity = new LaborInfoEntity();
        }
        if (this.supplierId != null) {
            entity.setSupplierId(this.supplierId);
        }
        if (this.corporationId != null) {
            entity.setSupplierCorporationId(this.corporationId);
        }
        if (this.customerId != null) {
            entity.setCustomerId(this.customerId);
        }
        if (this.contractId != null) {
            entity.setContractId(this.contractId);
        }
        entity.setEmpStatus(EmpStatusEnum.ON_THE_JOB.name());
        if (this.education != null) {
            entity.setEducation(this.education);
        }
        if (this.nativeField != null) {
            entity.setNativeField(this.nativeField);
        }
        if (this.householdRegistrationType != null) {
            entity.setHouseholdRegistrationType(this.householdRegistrationType);
        }
        if (this.householdCity != null) {
            entity.setHouseholdCity(this.householdCity);
        }
        if (this.householdAddress != null) {
            entity.setHouseholdAddress(this.householdAddress);
        }
        if (this.maritalStatus != null) {
            entity.setMaritalStatus(this.maritalStatus);
        }
        if (this.children != null) {
            entity.setChildren(this.children);
        }
        if (this.nation != null) {
            entity.setNation(this.nation);
        }
        if (this.political != null) {
            entity.setPolitical(this.political);
        }
        if (this.inWorkDay != null) {
            entity.setInWorkDay(this.inWorkDay);
        }
        if (this.deptId != null) {
            entity.setDeptId(this.deptId);
        }
        if (this.employed != null) {
            entity.setEmployed(this.employed);
        }
        if (this.joinDate != null) {
            entity.setJoinDate(this.joinDate);
        }
        if (this.post != null) {
            entity.setPost(this.post);
        }
        if (this.workEmail != null) {
            entity.setWorkEmail(this.workEmail);
        }
        if (this.wechat != null) {
            entity.setWechat(this.wechat);
        }
        if (this.personalWechat != null) {
            entity.setPersonalWechat(this.personalWechat);
        }
        if (this.workMobile != null) {
            entity.setWorkMobile(this.workMobile);
        }
        if (this.mobileNumber != null) {
            entity.setMobileNumber(this.mobileNumber);
        }
        if (this.personalEmail != null) {
            entity.setPersonalEmail(this.personalEmail);
        }
        if (this.address != null) {
            entity.setAddress(this.address);
        }
        if (this.bankCard != null) {
            entity.setBankCard(this.bankCard);
        }
        if (this.openCardCity != null) {
            entity.setOpenCardCity(this.openCardCity);
        }
        if (this.cardBank != null) {
            entity.setCardBank(this.cardBank);
        }
        if (this.bankBranch != null) {
            entity.setBankBranch(this.bankBranch);
        }
        entity.setJoinDate(LocalDate.now());
        return entity;
    }
}