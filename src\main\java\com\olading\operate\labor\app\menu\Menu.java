package com.olading.operate.labor.app.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Menu {

    /**
     * 标题
     */
    private String title;

    /**
     * 菜单链接的完整路径
     */
    private String routePath;

    /**
     * 需要的权限
     */
    private String authority;

    private List<Menu> children;
}
