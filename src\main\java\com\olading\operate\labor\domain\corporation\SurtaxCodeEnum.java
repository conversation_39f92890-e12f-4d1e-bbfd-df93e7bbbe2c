package com.olading.operate.labor.domain.corporation;

/**
 * @description:
 * @author: <PERSON><PERSON>ngweifeng
 * @time: 2025/7/18 18:54
 */
public enum SurtaxCodeEnum {
    /**
     * 城建税
     */
    URBAN_MAINTENANCE_TAX("城建税"),
    /**
     * 教育费附加
     */
    EDUCATION_SURCHARGE("教育费附加"),
    /**
     * 地方教育附加
     */
    LOCAL_EDUCATION_SURCHARGE("地方教育附加");

    private final String name;
    SurtaxCodeEnum(String name) {
        this.name = name;
    }
}
