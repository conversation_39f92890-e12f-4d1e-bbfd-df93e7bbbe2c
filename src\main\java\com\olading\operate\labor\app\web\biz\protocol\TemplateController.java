package com.olading.operate.labor.app.web.biz.protocol;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.ProtocolTempEnum;
import com.olading.operate.labor.app.web.biz.protocol.vo.AddUpdateTemplateVo;
import com.olading.operate.labor.app.web.biz.protocol.vo.SingleResponse;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.ProtocolTemplateQuery;
import com.olading.operate.labor.domain.service.ProtocolTemplateService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTemplateEntity;
import com.olading.operate.labor.domain.share.protocol.ProtocolTemplateRepository;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "合同模板接口")
@RestController
@RequestMapping("/api/supplier/protocol/template")
@RequiredArgsConstructor
@Slf4j
public class TemplateController extends BusinessController {

    public final QueryService queryService;

    public final ProtocolTemplateRepository  protocolTemplateRepository;
    public final ProtocolTemplateService protocolTemplateService;

    @Operation(summary = "判断模板名称是否已存在")
    @PostMapping(value = "checkTempName")
    public WebApiResponse<?> checkTempName(HttpServletRequest request, @RequestBody AddUpdateTemplateVo param) {
        QueryFilter<ProtocolTemplateQuery.Filters> filter = new QueryFilter<>();
        ProtocolTemplateQuery.Filters f = new ProtocolTemplateQuery.Filters();
        f.setTempName(param.getTemplateName());
        filter.setFilters(f);
        DataSet<CorporationProtocolTemplateEntity> dataSet = queryService.queryProtocolTemplate(filter);
        if (dataSet.getData() == null || dataSet.getData().isEmpty()) {
            throw new ApiException("模板名称已存在", ApiException.API_PARAM_ERROR);
        }
        return new WebApiResponse<>();
    }


    @Operation(summary = "创建模板第一步：创建/编辑模板")
    @PostMapping(value = "create")
    public WebApiResponse<SingleResponse<Long>> addUpdateTemplate(@RequestBody AddUpdateTemplateVo param) {
        param.doValidate();
        SupplierAndUser supplierAndUser = new SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        Long templateId = protocolTemplateService.createTemplate(param, supplierAndUser);
        return WebApiResponse.success(SingleResponse.of(templateId));
    }


    @Operation(summary = "创建模板第二步：模板设置（返回url和token，跳转至设置页面）")
    @PostMapping(value = "setTemplate")
    public WebApiResponse<SetTemplateResponse> setTemplate(@RequestBody SingleResponse<Long> request) {
        SetTemplateResponse setTemplateUrl = protocolTemplateService.getSetTemplateUrl(currentUser().getId(), request.getData());
        return WebApiResponse.success(setTemplateUrl);
    }


    @Operation(summary = "合同模板列表查询")
    @PostMapping(value = "list")
    public WebApiQueryResponse<TempListVo> tempList(@RequestBody QueryFilter<TempListFilter> param) {
        Long supplier = currentSupplier().getId();
        param.getFilters().setSupplier(supplier);
        QueryFilter<ProtocolTemplateQuery.Filters> filter = param.convert(TemplateController.TempListFilter::convert);
        DataSet<CorporationProtocolTemplateEntity> result = queryService.queryProtocolTemplate(filter);
        return WebApiQueryResponse.success(result.getData().stream().map(TempListVo::toVo).toList(), result.getTotal());
    }

    @Operation(summary = "修改模板状态")
    @PostMapping(value = "updateTempStatus")
    public WebApiResponse<?> updateTempStatus(HttpServletRequest request, @RequestBody UpdateTempStatusVo updateTempStatusDto) {
        protocolTemplateRepository.updateProtocolTempStatus(updateTempStatusDto.getTempId(), updateTempStatusDto.getStatus());
        return WebApiResponse.success();
    }


    @Operation(summary = "获取模板")
    @PostMapping(value = "getTemplateDetail")
    public WebApiResponse<AddUpdateTemplateVo> getTemplateDetail(HttpServletRequest request, Long tempId) {
        SupplierAndUser supplierAndUser = new SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        AddUpdateTemplateVo resp = protocolTemplateService.getTemplateDetail(tempId);
        return WebApiResponse.success(resp);
    }


    @Operation(summary = "查询合同模板是否可编辑")
    @GetMapping("canUpdate")
    public WebApiResponse<SingleResponse<Boolean>> tryUpdate(@RequestParam("id") Long id) {
        return WebApiResponse.success(SingleResponse.of(true));
    }

    //模板列表查询
    @Getter
    @Setter
    public static class SetTemplateResponse {

        @Schema(description = "请求token")
        private String token;
        @Schema(description = "请求url")
        private String url;
    }


    @Getter
    @Setter
    @AllArgsConstructor
    public static class SupplierAndUser {

        @Schema(description = "用户")
        private UserEntity user;
        @Schema(description = "作业主体")
        private SupplierEntity supplier;

        private TenantInfo tenant;

        private String tenantId;

        public SupplierAndUser(UserEntity user, SupplierEntity supplier, TenantInfo tenant) {
            this.user = user;
            this.supplier = supplier;
            this.tenantId = tenant.getId();
            this.tenant = tenant;
        }
    }


    @Schema(description=("模板上传请求参数"))
    @Getter
    @Setter
    public static class UploadTempDto {

        @Schema(description = "模板id")
        private Long tempId;
        @Schema(description = "文档id")
        private String archiveId;
    }

    @Schema(description="设置模板响应参数")
    @Getter
    @Setter
    public static class SetTemplateVo {

        @Schema(description = "请求token")
        private String token;
        @Schema(description = "请求url")
        private String url;
    }

    @Getter
    @Setter
    public static class GetTemplateDetailVo extends AddUpdateTemplateVo {

        private String cloudSigningTemplateId;

        private List<String> archives;

    }

    @Schema(description="更新模板状态dto")
    @Getter
    @Setter
    public static class UpdateTempStatusVo {
        @Schema(description = "模板id")
        private Long tempId;
        @Schema(description = "模板状态")
        private ProtocolTempEnum status;
    }


    @Schema(description="作业主体")
    @Getter
    @Setter
    public static class Corporation {
        @Schema(description = "作业主体id")
        private Long corporation;
        @Schema(description = "作业主体名")
        private String corporationName;
    }

    @Schema(description="模板查询条件")
    @Getter
    @Setter
    public static class TempListFilter{
        @Schema(description = "模板名称")
        private String tempName;
        @Schema(description = "模板状态")
        private ProtocolTempEnum tempStatus;
        @Schema(description = "租户id")
        private Long supplier;
        @Schema(description = "模板名关键字")
        private String tempNameWord;
        @Schema(description = "适用的作业主体id")
        private Long corporationId;
        public ProtocolTemplateQuery.Filters convert() {
            return Beans.copyBean(this, ProtocolTemplateQuery.Filters.class);
        }
    }
    @Schema(description="模板查询条件")
    @Getter
    @Setter
    public static class TempListVo{
        @Schema(description = "模板id")
        private Long tempId;
        @Schema(description = "模板名称")
        private String tempName;
        @Schema(description = "模板类型")
        private String tempType;
        @Schema(description = "模板状态 DRAFT：草稿，ENABLED：启用， DISABLED：停用， ERROR：异常")
        private String tempStatus;
        @Schema(description = "更新时间")
        private LocalDateTime modifiedTime;

        public static TempListVo toVo(CorporationProtocolTemplateEntity entity) {
            TempListVo tempListVo = new TempListVo();
            tempListVo.setTempId(entity.getId());
            tempListVo.setTempName(entity.getTempName());
            tempListVo.setTempType(entity.getTempType());
            tempListVo.setModifiedTime(entity.getModifyTime());
            tempListVo.setTempStatus(StringUtils.isNotBlank(entity.getStatus())? entity.getStatus() : null);
            return tempListVo;
        }

    }
}
