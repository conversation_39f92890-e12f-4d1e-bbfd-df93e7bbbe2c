package com.olading.operate.labor.app.web.biz.salary.vo;


import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.validation.constraints.Name;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PreviousIncomeImportFailData {
    @Schema(description = "作业主体ID")
    @NotNull(message = "作业主体不能为空")
    private Long supplierCorporationId;

    @Schema(description = "税款所属期")
    @NotNull(message = "税款所属期不能为空")
    //格式：yyyy-MM
    private String taxPeriod;

    @Schema(description = "服务商ID")
    private Long supplierId;

    @ExcelColumn(name = "反馈信息")
    @Name(required = true, maxLength = 60)
    private String errorMsg;
}
