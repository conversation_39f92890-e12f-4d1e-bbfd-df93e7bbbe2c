package com.olading.operate.labor.domain.share.submission;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.service.InfoSubmissionEnterpriseService;
import com.olading.operate.labor.domain.service.InfoSubmissionLaborService;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborInsertParam;
import com.olading.operate.labor.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class SubmissionInfoTest extends BaseTest {

    @Autowired
    private InfoSubmissionLaborService infoSubmissionLaborService;

    @Autowired
    private InfoSubmissionEnterpriseService infoSubmissionEnterpriseService;

    @Test
    public void testInsertInfoSubmissionLabor_Success() {
        log.info("开始测试插入人员信息报送记录 - 成功场景");

        // 准备测试数据
        Long supplierId = 9L;
        Long supplierCorporationId = 1L;
        String idCard = "350521199103105551";
        // 创建插入参数
        InfoSubmissionLaborInsertParam param = new InfoSubmissionLaborInsertParam(supplierId, supplierCorporationId, idCard);

        // 执行插入操作
        InfoSubmissionLaborEntity result = infoSubmissionLaborService.insertInfoSubmissionLabor(param);



        log.info("插入人员信息报送记录成功，记录 {}", JSONUtils.json(result));
    }

    @Test
    public void testInsertInfoSubmissionLabor_AlreadyExists() {
        log.info("开始测试插入人员信息报送记录 - 记录已存在场景");

        // 准备测试数据
        Long supplierId = 9L;
        Long supplierCorporationId = 1L;
        String idCard = "14010119900103235";

        // 先插入一条记录
        createTestData(supplierId, supplierCorporationId, idCard);
        InfoSubmissionLaborInsertParam param1 = new InfoSubmissionLaborInsertParam(supplierId, supplierCorporationId, idCard);
        InfoSubmissionLaborEntity firstResult = infoSubmissionLaborService.insertInfoSubmissionLabor(param1);
        assertNotNull(firstResult, "第一次插入应该成功");

        // 再次尝试插入相同的记录
        InfoSubmissionLaborInsertParam param2 = new InfoSubmissionLaborInsertParam(supplierId, supplierCorporationId, idCard);
        InfoSubmissionLaborEntity secondResult = infoSubmissionLaborService.insertInfoSubmissionLabor(param2);

        // 验证第二次插入返回null（记录已存在）
        assertNull(secondResult, "记录已存在时应该返回null");

        log.info("记录已存在测试通过");
    }



    /**
     * 创建完整的测试数据
     */
    private void createTestData(Long supplierId, Long supplierCorporationId, String idCard) {
        createTestLaborProtocol(supplierId, supplierCorporationId, idCard);
        SupplierLaborEntity supplierLabor = createTestSupplierLabor(supplierId, idCard);
        createTestLaborInfo(supplierLabor.getId());
    }

    /**
     * 创建测试用的劳务协议记录
     */
    private LaborProtocolEntity createTestLaborProtocol(Long supplierId, Long supplierCorporationId, String idCard) {
        return withTransaction(status -> {
            LaborProtocolEntity entity = new LaborProtocolEntity();
            entity.setSupplierId(supplierId);
            entity.setSupplierCorporationId(supplierCorporationId);
            entity.setIdCard(idCard);
            entity.setStartDate(LocalDate.now());
            entity.setEndDate(LocalDate.now().plusYears(1));
            return em.merge(entity);
        });
    }

    /**
     * 创建测试用的劳务人员记录
     */
    private SupplierLaborEntity createTestSupplierLabor(Long supplierId, String idCard) {
        return withTransaction(status -> {
            SupplierLaborEntity entity = new SupplierLaborEntity();
            entity.setSupplierId(supplierId);
            entity.setIdCard(idCard);
            entity.setName("测试用户");
            entity.setCellphone("***********");
            return em.merge(entity);
        });
    }

    /**
     * 创建测试用的劳务人员详细信息
     */
    private LaborInfoEntity createTestLaborInfo(Long laborId) {
        return withTransaction(status -> {
            LaborInfoEntity entity = new LaborInfoEntity();
            entity.setLaborId(laborId);
            entity.setHouseholdCity("北京市");
            entity.setHouseholdAddress("北京市朝阳区测试地址");
            entity.setCardBank("测试银行");
            entity.setBankCard("6222021234567890123");
            return em.merge(entity);
        });
    }



    @Test
    public void test(){
        InfoSubmissionEnterpriseEntity infoSubmissionEnterpriseEntity =
                infoSubmissionEnterpriseService.insertInfoSubmissionEnterprise(24l, 20l);
        log.info("插入企业信息报送记录成功，记录 {}", JSONUtils.json(infoSubmissionEnterpriseEntity));
    }
}
