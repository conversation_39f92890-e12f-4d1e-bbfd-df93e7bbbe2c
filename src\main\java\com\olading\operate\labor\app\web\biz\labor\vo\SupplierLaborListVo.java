package com.olading.operate.labor.app.web.biz.labor.vo;

import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
public class SupplierLaborListVo {

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "手机号,平台下唯一")
    private String cellphone;

    @Schema(description = "签约状态")
    private String signStatus;

    @Schema(description = "出生日期")
    private String birthdayDate;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "证件有效期")
    private String idCardPeriod;

    public SupplierLaborEntity toLaborEntity(Long supplierId, TenantInfo tenantInfo) {
        SupplierLaborEntity entity = new SupplierLaborEntity(tenantInfo, supplierId);
        entity.setName(this.name);
        entity.setIdCard(this.idCard);
        entity.setCellphone(this.cellphone);
        entity.setSupplierId(supplierId);
        if (this.birthdayDate != null) {
            entity.setBirthdayDate(this.birthdayDate);
        }
        if (this.idCardPeriod != null) {
            entity.setIdCardPeriod(this.idCardPeriod);
        }
        return entity;
    }
}
